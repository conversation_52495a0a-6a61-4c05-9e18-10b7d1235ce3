#!/usr/bin/env python3
"""
手动提示词生成器
为三个IP的未匹配单词生成可直接复制到AI工具的提示词
"""

import json
import os
from typing import List, Dict, Any

class ManualPromptGenerator:
    """手动提示词生成器"""
    
    def __init__(self):
        self.primary_words = self._load_primary_words()
        
        # IP角色信息
        self.ip_info = {
            "小猪佩奇": {
                "characters": "<PERSON><PERSON><PERSON> (佩奇), <PERSON> (乔治), <PERSON><PERSON> (猪妈妈), <PERSON> (猪爸爸), Grandpa <PERSON> (猪爷爷)",
                "setting": "温馨家庭生活",
                "themes": "家庭, 友谊, 日常生活, 学习",
                "examples": [
                    '"单词": "apple", "英文例句": "<PERSON><PERSON><PERSON> eats a red apple with <PERSON>.", "中文翻译": "佩奇和乔治一起吃红苹果。"',
                    '"单词": "happy", "英文例句": "<PERSON><PERSON><PERSON> is very happy today.", "中文翻译": "佩奇今天很开心。"'
                ]
            },
            "疯狂动物城": {
                "characters": "<PERSON> (兔子朱迪), <PERSON> (狐狸尼克), Chief <PERSON><PERSON> (水牛局长), <PERSON> (树懒闪电)",
                "setting": "现代动物城市",
                "themes": "友谊, 正义, 梦想, 合作",
                "examples": [
                    '"单词": "apple", "英文例句": "Judy eats a fresh apple for lunch.", "中文翻译": "朱迪午餐吃了一个新鲜苹果。"',
                    '"单词": "fast", "英文例句": "Nick can run very fast.", "中文翻译": "尼克跑得很快。"'
                ]
            },
            "功夫熊猫": {
                "characters": "Po (熊猫阿宝), Master Shifu (师父), Tigress (娇虎), Viper (蛇蝰), Crane (仙鹤)",
                "setting": "中国古代功夫世界", 
                "themes": "功夫训练, 友谊, 勇气, 成长",
                "examples": [
                    '"单词": "apple", "英文例句": "Po eats an apple after training.", "中文翻译": "阿宝训练后吃了一个苹果。"',
                    '"单词": "strong", "英文例句": "Po becomes very strong.", "中文翻译": "阿宝变得很强壮。"'
                ]
            }
        }
    
    def _load_primary_words(self) -> set:
        """加载531个小学单词表"""
        words = set()
        try:
            with open('data/小学单词表.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    word = line.strip().lower()
                    if word:
                        words.add(word)
            print(f"✅ 成功加载小学词汇表，共 {len(words)} 个单词")
        except Exception as e:
            print(f"❌ 加载小学词汇表失败: {e}")
        return words
    
    def _get_word_difficulty_level(self, word: str) -> str:
        """根据单词特征判断难度等级"""
        word = word.lower()
        
        # Level 1: 基础高频词汇 (1-2年级)
        level1_words = {
            'a', 'an', 'and', 'at', 'be', 'am', 'is', 'are', 'big', 'cat', 'dog', 'eat', 'go', 
            'he', 'i', 'in', 'it', 'my', 'no', 'on', 'see', 'the', 'to', 'up', 'we', 'you',
            'red', 'blue', 'yes', 'box', 'bag', 'car', 'run', 'sit', 'sun', 'hat', 'pen'
        }
        
        # Level 2: 中等词汇 (3-4年级)
        level2_words = {
            'about', 'after', 'always', 'animal', 'ask', 'because', 'before', 'come', 'do',
            'find', 'for', 'from', 'good', 'have', 'help', 'here', 'how', 'know', 'like',
            'look', 'make', 'new', 'now', 'old', 'play', 'right', 'say', 'she', 'some',
            'take', 'tell', 'them', 'there', 'they', 'think', 'this', 'time', 'want', 'what',
            'when', 'where', 'will', 'with', 'work', 'would', 'your', 'happy', 'family'
        }
        
        if word in level1_words or len(word) <= 3:
            return "level1"
        elif word in level2_words or len(word) <= 5:
            return "level2"
        else:
            return "level3"
    
    def load_missing_words_from_report(self, report_file: str) -> Dict[str, List[str]]:
        """从分析报告中加载未匹配单词"""
        missing_words = {}
        
        try:
            with open(report_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析报告内容
            lines = content.split('\n')
            current_ip = None
            in_code_block = False
            
            for line in lines:
                # 检测IP标题
                if "### 功夫熊猫 - 未匹配的" in line:
                    current_ip = "功夫熊猫"
                    in_code_block = False
                elif "### 疯狂动物城 - 未匹配的" in line:
                    current_ip = "疯狂动物城"  
                    in_code_block = False
                elif "### 小猪佩奇 - 未匹配的" in line:
                    current_ip = "小猪佩奇"
                    in_code_block = False
                
                # 检测代码块开始
                elif line.strip() == "```" and current_ip and not in_code_block:
                    in_code_block = True
                
                # 提取单词列表（在代码块内的非空行）
                elif in_code_block and current_ip and line.strip() and line.strip() != "```":
                    words_line = line.strip()
                    if words_line:
                        words = [word.strip() for word in words_line.split(',') if word.strip()]
                        missing_words[current_ip] = words
                        in_code_block = False
                        current_ip = None
                
                # 检测代码块结束
                elif line.strip() == "```" and in_code_block:
                    in_code_block = False
        
        except Exception as e:
            print(f"❌ 解析报告文件失败: {e}")
        
        return missing_words
    
    def generate_batch_prompt(self, words: List[str], ip_name: str, batch_number: int) -> str:
        """生成单个批次的提示词"""
        
        words_str = ", ".join(words)
        ip_info = self.ip_info[ip_name]
        
        # 将单词按难度分组
        words_by_level = {"level1": [], "level2": [], "level3": []}
        for word in words:
            level = self._get_word_difficulty_level(word)
            words_by_level[level].append(word)
        
        # 生成小学词汇约束字符串（前100个作为示例）
        allowed_words_sample = ", ".join(sorted(list(self.primary_words))[:100])
        
        prompt = f"""你是一位专业的小学英语教师，需要为以下单词创建{ip_name}主题的英语例句。

**目标单词列表**: {words_str}

**{ip_name}角色信息**:
- 主要角色: {ip_info['characters']}
- 故事背景: {ip_info['setting']}
- 主要主题: {ip_info['themes']}

**严格的小学英语词汇约束**:
你只能使用531个小学词汇表中的单词，包括但不限于：
{allowed_words_sample}...等531个词汇

**绝对禁止使用的词汇类型**:
- 超出小学范围的复杂词汇（如：magnificent, extraordinary, tremendous, sophisticated等）
- 专业术语（如：technology, environment, responsibility, psychology等）
- 抽象概念词（如：philosophy, democracy, capitalism等）
- 复杂动词时态（如：had been doing, will have done, would have been等）

**分级难度要求**:

1. **1-2年级词汇** ({', '.join(words_by_level['level1']) if words_by_level['level1'] else '无'}):
   - 句长：3-5个词
   - 语法：主谓宾结构，一般现在时
   - 示例：Peppa is happy. / George likes toys. / We eat apples.

2. **3-4年级词汇** ({', '.join(words_by_level['level2']) if words_by_level['level2'] else '无'}):
   - 句长：4-7个词  
   - 语法：现在进行时，简单疑问句
   - 示例：Peppa is playing with George. / Do you like music?

3. **5-6年级词汇** ({', '.join(words_by_level['level3']) if words_by_level['level3'] else '无'}):
   - 句长：5-8个词
   - 语法：一般过去时，情态动词can/should
   - 示例：Peppa learned to swim yesterday. / You should be careful.

**生成要求**:
1. 每个单词生成1个例句，必须包含该单词
2. 例句必须融入{ip_name}的角色和场景
3. 80%的例句控制在4-6个词以内
4. 句子结构简单，避免复杂从句
5. 提供准确的中文翻译
6. 确保所有词汇都在531个小学词汇表内

**输出格式** (严格JSON格式):
```json
[
  {{
    {ip_info['examples'][0]}
  }},
  {{
    {ip_info['examples'][1]}
  }}
]
```

**质量检查清单**:
- ✅ 每个例句都包含目标单词
- ✅ 所有词汇都在531个小学词汇表内
- ✅ 句子长度符合难度要求
- ✅ 语法结构适合小学生
- ✅ 融入了{ip_name}的角色或场景
- ✅ 中文翻译准确自然

请为所有单词生成例句，确保JSON格式正确且符合所有要求。"""

        return prompt
    
    def generate_all_prompts(self, report_file: str = "ip_words_analysis_report.md", batch_size: int = 20):
        """生成所有IP的提示词"""
        
        print("🚀 开始生成手动提示词...")
        
        # 加载未匹配单词
        missing_words_dict = self.load_missing_words_from_report(report_file)
        
        if not missing_words_dict:
            print("❌ 未找到未匹配单词数据")
            return
        
        # 创建输出目录
        output_dir = "manual_prompts"
        os.makedirs(output_dir, exist_ok=True)
        
        total_prompts = 0
        
        for ip_name, words in missing_words_dict.items():
            print(f"\n🎬 处理 {ip_name}，共 {len(words)} 个单词")
            
            # 分批处理
            batches = [words[i:i+batch_size] for i in range(0, len(words), batch_size)]
            
            ip_dir = os.path.join(output_dir, ip_name)
            os.makedirs(ip_dir, exist_ok=True)
            
            # 生成总览文件
            overview_content = f"""# {ip_name} 手动提示词总览

## 📊 基本信息
- **总单词数**: {len(words)} 个
- **批次数量**: {len(batches)} 个
- **每批大小**: {batch_size} 个单词

## 📋 批次列表
"""
            
            for batch_idx, batch in enumerate(batches, 1):
                batch_file = f"batch_{batch_idx}.txt"
                
                # 生成批次提示词
                prompt = self.generate_batch_prompt(batch, ip_name, batch_idx)
                
                # 保存批次文件
                with open(os.path.join(ip_dir, batch_file), 'w', encoding='utf-8') as f:
                    f.write(f"# {ip_name} - 批次 {batch_idx}\n\n")
                    f.write(f"**单词数量**: {len(batch)}\n")
                    f.write(f"**目标单词**: {', '.join(batch)}\n\n")
                    f.write("## 🎯 使用说明\n\n")
                    f.write("1. 复制下面的提示词\n")
                    f.write("2. 粘贴到ChatGPT、Claude或其他AI工具中\n")
                    f.write("3. 获得JSON格式的例句结果\n")
                    f.write("4. 检查结果质量并进行必要调整\n\n")
                    f.write("## 📝 提示词内容\n\n")
                    f.write("```\n")
                    f.write(prompt)
                    f.write("\n```\n")
                
                # 更新总览
                overview_content += f"- **批次 {batch_idx}**: {len(batch)} 个单词 - `{batch_file}`\n"
                overview_content += f"  - 单词: {', '.join(batch[:5])}{'...' if len(batch) > 5 else ''}\n"
                
                total_prompts += 1
                print(f"   ✅ 批次 {batch_idx}: {len(batch)} 个单词")
            
            # 添加使用指南
            overview_content += f"""

## 🔧 使用流程

1. **选择批次**: 从上面的批次列表中选择一个批次文件
2. **复制提示词**: 打开批次文件，复制提示词内容
3. **AI生成**: 粘贴到ChatGPT等AI工具中，获得JSON结果
4. **质量检查**: 检查生成的例句是否符合要求
5. **保存结果**: 将结果保存为JSON文件

## 📋 质量检查要点

- ✅ 每个例句都包含目标单词
- ✅ 所有词汇都在小学词汇表内
- ✅ 句子长度适合小学生
- ✅ 融入了{ip_name}的角色和场景
- ✅ 中文翻译准确自然
- ✅ JSON格式正确

## 📞 技术支持

如果遇到问题，请检查：
1. 提示词是否完整复制
2. AI工具是否支持长文本输入
3. 生成的JSON格式是否正确
4. 例句质量是否符合小学标准
"""
            
            # 保存总览文件
            with open(os.path.join(ip_dir, "README.md"), 'w', encoding='utf-8') as f:
                f.write(overview_content)
            
            print(f"   📁 {ip_name} 完成，生成 {len(batches)} 个批次")
        
        # 生成全局总览
        global_overview = f"""# 手动提示词生成系统 🎯

## 📊 总体统计

- **处理IP数量**: {len(missing_words_dict)} 个
- **总单词数**: {sum(len(words) for words in missing_words_dict.values())} 个
- **总批次数**: {total_prompts} 个
- **生成时间**: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 🎬 IP详情

"""
        
        for ip_name, words in missing_words_dict.items():
            batches_count = len([words[i:i+batch_size] for i in range(0, len(words), batch_size)])
            global_overview += f"### {ip_name}\n"
            global_overview += f"- **单词数**: {len(words)} 个\n"
            global_overview += f"- **批次数**: {batches_count} 个\n"
            global_overview += f"- **文件夹**: `{ip_name}/`\n\n"
        
        global_overview += """## 🚀 快速开始

1. **选择IP**: 进入对应的IP文件夹
2. **查看总览**: 阅读 `README.md` 了解详情
3. **选择批次**: 选择一个批次文件开始
4. **复制提示词**: 复制提示词到AI工具
5. **生成例句**: 获得高质量的例句结果

## 📋 系统特点

- ✅ **严格词汇控制**: 只使用531个小学词汇
- ✅ **分级难度**: 按1-6年级分级生成
- ✅ **主题融合**: 深度融入IP角色和场景
- ✅ **批量处理**: 支持大规模单词处理
- ✅ **质量保证**: 多重质量检查机制

---
*由改进的AI例句仿写系统生成*
"""
        
        with open(os.path.join(output_dir, "README.md"), 'w', encoding='utf-8') as f:
            f.write(global_overview)
        
        print(f"\n🎉 手动提示词生成完成！")
        print(f"📊 总计: {len(missing_words_dict)} 个IP，{total_prompts} 个批次")
        print(f"📁 输出目录: {output_dir}")
        print(f"📖 使用指南: {output_dir}/README.md")

def main():
    """主函数"""
    generator = ManualPromptGenerator()
    generator.generate_all_prompts()

if __name__ == "__main__":
    main()
