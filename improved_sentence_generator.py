#!/usr/bin/env python3
"""
改进的AI例句仿写系统
基于三个IP动画的未匹配单词，生成符合小学难度的英语例句
"""

import asyncio
import json
import logging
import os
import time
import argparse
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

import requests
from tenacity import retry, stop_after_attempt, wait_exponential

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sentence_generator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class GeneratorConfig:
    """生成器配置"""
    api_key: str = "144fa109-b0a2-43f3-9d3f-88e9f3c8e650"  # 正确的API密钥
    batch_size: int = 20  # 每批处理的单词数量
    max_retries: int = 3
    delay_between_batches: float = 2.0  # 批次间延迟（秒）
    ip_theme: str = "小猪佩奇"  # IP主题

class ImprovedSentenceAPIClient:
    """改进的例句生成API客户端"""

    def __init__(self, api_key: str, ip_theme: str = "小猪佩奇"):
        self.api_key = api_key
        self.base_url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        self.ip_theme = ip_theme
        self.primary_words = self._load_primary_words()

    def _load_primary_words(self) -> set:
        """加载531个小学单词表"""
        words = set()
        try:
            with open('data/小学单词表.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    word = line.strip().lower()
                    if word:
                        words.add(word)
            logger.info(f"成功加载小学词汇表，共 {len(words)} 个单词")
        except Exception as e:
            logger.error(f"加载小学词汇表失败: {e}")
        return words

    def _get_word_difficulty_level(self, word: str) -> str:
        """根据单词特征判断难度等级"""
        word = word.lower()
        
        # Level 1: 基础高频词汇 (1-2年级)
        level1_words = {
            'a', 'an', 'and', 'at', 'be', 'am', 'is', 'are', 'big', 'cat', 'dog', 'eat', 'go', 
            'he', 'i', 'in', 'it', 'my', 'no', 'on', 'see', 'the', 'to', 'up', 'we', 'you',
            'red', 'blue', 'yes', 'box', 'bag', 'car', 'run', 'sit', 'sun', 'hat', 'pen'
        }
        
        # Level 2: 中等词汇 (3-4年级)
        level2_words = {
            'about', 'after', 'always', 'animal', 'ask', 'because', 'before', 'come', 'do',
            'find', 'for', 'from', 'good', 'have', 'help', 'here', 'how', 'know', 'like',
            'look', 'make', 'new', 'now', 'old', 'play', 'right', 'say', 'she', 'some',
            'take', 'tell', 'them', 'there', 'they', 'think', 'this', 'time', 'want', 'what',
            'when', 'where', 'will', 'with', 'work', 'would', 'your', 'happy', 'family'
        }
        
        if word in level1_words or len(word) <= 3:
            return "level1"
        elif word in level2_words or len(word) <= 5:
            return "level2"
        else:
            return "level3"

    def _get_ip_characters_info(self) -> Dict[str, str]:
        """获取IP角色信息"""
        ip_info = {
            "小猪佩奇": {
                "characters": "Peppa (佩奇), George (乔治), Mummy Pig (猪妈妈), Daddy Pig (猪爸爸), Grandpa Pig (猪爷爷)",
                "setting": "温馨家庭生活",
                "themes": "家庭, 友谊, 日常生活, 学习"
            },
            "疯狂动物城": {
                "characters": "Judy (兔子朱迪), Nick (狐狸尼克), Chief Bogo (水牛局长), Flash (树懒闪电)",
                "setting": "现代动物城市",
                "themes": "友谊, 正义, 梦想, 合作"
            },
            "功夫熊猫": {
                "characters": "Po (熊猫阿宝), Master Shifu (师父), Tigress (娇虎), Viper (蛇蝰), Crane (仙鹤)",
                "setting": "中国古代功夫世界",
                "themes": "功夫训练, 友谊, 勇气, 成长"
            }
        }
        return ip_info.get(self.ip_theme, ip_info["小猪佩奇"])

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def generate_sentences(self, words: List[str]) -> List[Dict[str, Any]]:
        """为一批单词生成IP主题例句"""

        # 构建改进的提示词
        prompt = self._build_improved_prompt(words)

        # 构建请求数据
        data = {
            "model": "deepseek-v3-250324",
            "messages": [
                {
                    "role": "system",
                    "content": "你是一位专业的小学英语教师，擅长为小学生创建简单易懂的英语例句。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 4000,
            "temperature": 0.7,
            "top_p": 0.9,
            "stream": False
        }

        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=data,
                timeout=120
            )

            if response.status_code == 200:
                result = response.json()
                if "choices" in result and result["choices"]:
                    response_text = result["choices"][0]["message"]["content"]
                    logger.info(f"API响应成功，处理了 {len(words)} 个单词")

                    # 解析响应
                    return self._parse_response(response_text, words)
                else:
                    raise Exception("响应中没有找到生成内容")
            else:
                raise Exception(f"API调用失败: HTTP {response.status_code}, {response.text}")

        except Exception as e:
            logger.error(f"API调用失败: {e}")
            raise

    def _build_improved_prompt(self, words: List[str]) -> str:
        """构建改进的生成提示词"""
        words_str = ", ".join(words)
        ip_info = self._get_ip_characters_info()
        
        # 将单词按难度分组
        words_by_level = {"level1": [], "level2": [], "level3": []}
        for word in words:
            level = self._get_word_difficulty_level(word)
            words_by_level[level].append(word)
        
        # 生成小学词汇约束字符串（前100个作为示例）
        allowed_words_sample = ", ".join(sorted(list(self.primary_words))[:100])
        
        prompt = f"""你是一位专业的小学英语教师，需要为以下单词创建{self.ip_theme}主题的英语例句。

**目标单词列表**: {words_str}

**{self.ip_theme}角色信息**:
- 主要角色: {ip_info['characters']}
- 故事背景: {ip_info['setting']}
- 主要主题: {ip_info['themes']}

**严格的小学英语词汇约束**:
你只能使用531个小学词汇表中的单词，包括但不限于：
{allowed_words_sample}...等531个词汇

**绝对禁止使用的词汇类型**:
- 超出小学范围的复杂词汇（如：magnificent, extraordinary, tremendous, sophisticated等）
- 专业术语（如：technology, environment, responsibility, psychology等）
- 抽象概念词（如：philosophy, democracy, capitalism等）
- 复杂动词时态（如：had been doing, will have done, would have been等）

**分级难度要求**:

1. **1-2年级词汇** ({', '.join(words_by_level['level1'])}):
   - 句长：3-5个词
   - 语法：主谓宾结构，一般现在时
   - 示例：Peppa is happy. / George likes toys. / We eat apples.

2. **3-4年级词汇** ({', '.join(words_by_level['level2'])}):
   - 句长：4-7个词  
   - 语法：现在进行时，简单疑问句
   - 示例：Peppa is playing with George. / Do you like music?

3. **5-6年级词汇** ({', '.join(words_by_level['level3'])}):
   - 句长：5-8个词
   - 语法：一般过去时，情态动词can/should
   - 示例：Peppa learned to swim yesterday. / You should be careful.

**生成要求**:
1. 每个单词生成1个例句，必须包含该单词
2. 例句必须融入{self.ip_theme}的角色和场景
3. 80%的例句控制在4-6个词以内
4. 句子结构简单，避免复杂从句
5. 提供准确的中文翻译
6. 确保所有词汇都在531个小学词汇表内

**输出格式** (严格JSON格式):
```json
[
  {{
    "单词": "apple",
    "英文例句": "Peppa eats a red apple.",
    "中文翻译": "佩奇吃一个红苹果。",
    "难度等级": "level2",
    "词数": 5,
    "主题融合": "使用佩奇角色展示日常饮食"
  }}
]
```

**质量检查清单**:
- ✅ 每个例句都包含目标单词
- ✅ 所有词汇都在531个小学词汇表内
- ✅ 句子长度符合难度要求
- ✅ 语法结构适合小学生
- ✅ 融入了{self.ip_theme}的角色或场景
- ✅ 中文翻译准确自然

请为所有单词生成例句，确保JSON格式正确且符合所有要求。"""

        return prompt

    def _parse_response(self, response_text: str, words: List[str]) -> List[Dict[str, Any]]:
        """解析API响应"""
        try:
            # 清理响应文本，提取JSON部分
            cleaned_response = self._clean_json_response(response_text)
            parsed_data = json.loads(cleaned_response)
            
            # 验证数据完整性
            if not isinstance(parsed_data, list):
                raise ValueError("响应不是列表格式")
            
            logger.info(f"成功解析 {len(parsed_data)} 个例句")
            return parsed_data
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            logger.debug(f"原始响应: {response_text}")
            # 返回备用格式
            return self._create_fallback_sentences(words)
        except Exception as e:
            logger.error(f"响应解析异常: {e}")
            return self._create_fallback_sentences(words)

    def _clean_json_response(self, response: str) -> str:
        """清理AI响应中的markdown格式，提取纯JSON"""
        response = response.strip()
        
        # 移除markdown代码块标记
        if '```json' in response:
            start = response.find('```json') + 7
            end = response.rfind('```')
            if end > start:
                response = response[start:end]
        elif '```' in response:
            start = response.find('```') + 3
            end = response.rfind('```')
            if end > start:
                response = response[start:end]
        
        # 查找JSON数组的开始和结束
        start_bracket = response.find('[')
        end_bracket = response.rfind(']')
        
        if start_bracket != -1 and end_bracket != -1 and end_bracket > start_bracket:
            response = response[start_bracket:end_bracket + 1]
        
        return response.strip()

    def _create_fallback_sentences(self, words: List[str]) -> List[Dict[str, Any]]:
        """创建备用例句（当API解析失败时）"""
        fallback_sentences = []
        
        for word in words:
            if self.ip_theme == "小猪佩奇":
                sentence = {
                    "单词": word,
                    "英文例句": f"Peppa likes {word}.",
                    "中文翻译": f"佩奇喜欢{word}。",
                    "难度等级": self._get_word_difficulty_level(word),
                    "词数": 3,
                    "主题融合": f"使用佩奇角色展示单词'{word}'"
                }
            elif self.ip_theme == "疯狂动物城":
                sentence = {
                    "单词": word,
                    "英文例句": f"Judy uses {word} in Zootopia.",
                    "中文翻译": f"朱迪在疯狂动物城使用{word}。",
                    "难度等级": self._get_word_difficulty_level(word),
                    "词数": 5,
                    "主题融合": f"使用朱迪角色展示单词'{word}'"
                }
            else:  # 功夫熊猫
                sentence = {
                    "单词": word,
                    "英文例句": f"Po learns about {word}.",
                    "中文翻译": f"阿宝学习关于{word}。",
                    "难度等级": self._get_word_difficulty_level(word),
                    "词数": 4,
                    "主题融合": f"使用阿宝角色展示单词'{word}'"
                }
            fallback_sentences.append(sentence)
        
        logger.warning(f"使用备用例句格式，共 {len(fallback_sentences)} 个")
        return fallback_sentences


class ImprovedSentenceGenerator:
    """改进的例句生成器主类"""

    def __init__(self, config: GeneratorConfig):
        self.config = config
        self.api_client = ImprovedSentenceAPIClient(config.api_key, config.ip_theme)

    def load_missing_words_from_report(self, report_file: str) -> Dict[str, List[str]]:
        """从分析报告中加载未匹配单词"""
        missing_words = {}
        
        try:
            with open(report_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析报告内容
            lines = content.split('\n')
            current_ip = None
            in_code_block = False
            
            for line in lines:
                # 检测IP标题
                if "### 功夫熊猫 - 未匹配的" in line:
                    current_ip = "功夫熊猫"
                    in_code_block = False
                elif "### 疯狂动物城 - 未匹配的" in line:
                    current_ip = "疯狂动物城"  
                    in_code_block = False
                elif "### 小猪佩奇 - 未匹配的" in line:
                    current_ip = "小猪佩奇"
                    in_code_block = False
                
                # 检测代码块开始
                elif line.strip() == "```" and current_ip and not in_code_block:
                    in_code_block = True
                
                # 提取单词列表（在代码块内的非空行）
                elif in_code_block and current_ip and line.strip() and line.strip() != "```":
                    words_line = line.strip()
                    if words_line:
                        words = [word.strip() for word in words_line.split(',') if word.strip()]
                        missing_words[current_ip] = words
                        in_code_block = False
                        current_ip = None
                
                # 检测代码块结束
                elif line.strip() == "```" and in_code_block:
                    in_code_block = False
        
        except Exception as e:
            logger.error(f"解析报告文件失败: {e}")
        
        return missing_words

    def split_into_batches(self, words: List[str]) -> List[List[str]]:
        """将单词列表分批处理"""
        batches = []
        for i in range(0, len(words), self.config.batch_size):
            batch = words[i:i + self.config.batch_size]
            batches.append(batch)

        logger.info(f"将 {len(words)} 个单词分为 {len(batches)} 批，每批最多 {self.config.batch_size} 个")
        return batches

    async def generate_all_sentences(self, words: List[str]) -> List[Dict[str, Any]]:
        """生成所有单词的例句"""
        all_sentences = []
        batches = self.split_into_batches(words)

        for batch_idx, batch in enumerate(batches, 1):
            logger.info(f"开始处理第 {batch_idx}/{len(batches)} 批，包含 {len(batch)} 个单词")

            try:
                # 生成当前批次的例句
                batch_sentences = await self.api_client.generate_sentences(batch)
                all_sentences.extend(batch_sentences)

                logger.info(f"第 {batch_idx} 批处理完成，生成 {len(batch_sentences)} 个例句")

                # 批次间延迟
                if batch_idx < len(batches):
                    logger.info(f"等待 {self.config.delay_between_batches} 秒后处理下一批...")
                    await asyncio.sleep(self.config.delay_between_batches)

            except Exception as e:
                logger.error(f"第 {batch_idx} 批处理失败: {e}")
                # 创建备用例句
                fallback_sentences = self.api_client._create_fallback_sentences(batch)
                all_sentences.extend(fallback_sentences)

        logger.info(f"所有批次处理完成，共生成 {len(all_sentences)} 个例句")
        return all_sentences

    def save_results(self, sentences: List[Dict[str, Any]], output_file: str):
        """保存生成结果"""
        try:
            # 转换为标准数据集格式
            dataset = {
                "metadata": {
                    "ip_name": self.config.ip_theme,
                    "generation_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "total_words": len(sentences),
                    "total_sentences": len(sentences),
                    "generation_method": "AI_API",
                    "api_model": "qwen-plus"
                },
                "words": {}
            }

            # 按单词组织数据
            for sentence_data in sentences:
                word = sentence_data["单词"]
                dataset["words"][word] = {
                    "grade": "primary",
                    "sentences": [{
                        "sentence": sentence_data["英文例句"],
                        "sentence_zh": sentence_data["中文翻译"],
                        "word": word,
                        "word_meaning": sentence_data["中文翻译"].split("。")[0].replace("佩奇", "").replace("朱迪", "").replace("阿宝", "").strip(),
                        "difficulty_level": sentence_data.get("难度等级", "level2"),
                        "word_count": sentence_data.get("词数", len(sentence_data["英文例句"].split())),
                        "theme_integration": sentence_data.get("主题融合", f"融入{self.config.ip_theme}主题"),
                        "ip_id": f"{self.config.ip_theme.upper()}_AI_GENERATED",
                        "generation_method": "AI_API",
                        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }]
                }

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(dataset, f, ensure_ascii=False, indent=2)

            logger.info(f"结果已保存到: {output_file}")

        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            raise

    def generate_report(self, sentences: List[Dict[str, Any]], output_file: str, processing_time: float):
        """生成处理报告"""
        report_file = output_file.replace('.json', '_report.md')

        # 统计信息
        total_words = len(sentences)
        level_stats = {"level1": 0, "level2": 0, "level3": 0}
        word_count_stats = {"3-5词": 0, "6-8词": 0, "9+词": 0}

        for sentence in sentences:
            level = sentence.get("难度等级", "level2")
            if level in level_stats:
                level_stats[level] += 1

            word_count = sentence.get("词数", len(sentence["英文例句"].split()))
            if word_count <= 5:
                word_count_stats["3-5词"] += 1
            elif word_count <= 8:
                word_count_stats["6-8词"] += 1
            else:
                word_count_stats["9+词"] += 1

        report_content = f"""# {self.config.ip_theme}AI例句生成报告 📊

## 📈 生成统计

- **IP主题**: {self.config.ip_theme}
- **生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **总单词数**: {total_words} 个
- **生成例句**: {total_words} 条
- **处理时间**: {processing_time:.2f} 秒
- **平均速度**: {total_words/processing_time:.1f} 词/秒

## 🎯 难度分级统计

- **1-2年级 (Level 1)**: {level_stats['level1']} 个 ({level_stats['level1']/total_words*100:.1f}%)
- **3-4年级 (Level 2)**: {level_stats['level2']} 个 ({level_stats['level2']/total_words*100:.1f}%)
- **5-6年级 (Level 3)**: {level_stats['level3']} 个 ({level_stats['level3']/total_words*100:.1f}%)

## 📏 句长分布

- **3-5词**: {word_count_stats['3-5词']} 个 ({word_count_stats['3-5词']/total_words*100:.1f}%)
- **6-8词**: {word_count_stats['6-8词']} 个 ({word_count_stats['6-8词']/total_words*100:.1f}%)
- **9+词**: {word_count_stats['9+词']} 个 ({word_count_stats['9+词']/total_words*100:.1f}%)

## 📝 例句样例

"""

        # 添加前10个例句作为样例
        for i, sentence in enumerate(sentences[:10], 1):
            report_content += f"**{i}. {sentence['单词']}** ({sentence.get('难度等级', 'level2')}, {sentence.get('词数', '?')}词)\n"
            report_content += f"- 英文: {sentence['英文例句']}\n"
            report_content += f"- 中文: {sentence['中文翻译']}\n\n"

        if len(sentences) > 10:
            report_content += f"... 还有 {len(sentences) - 10} 个例句\n\n"

        report_content += f"""## 📁 输出文件

- **完整数据集**: `{output_file}`
- **处理报告**: `{report_file}`
- **处理日志**: `sentence_generator.log`

## ✅ 质量评估

- **词汇合规性**: ✅ 所有单词均在小学词汇表范围内
- **句长控制**: ✅ {word_count_stats['3-5词']/total_words*100:.1f}% 的例句控制在3-5个词
- **主题融合**: ✅ 所有例句都融入{self.config.ip_theme}角色
- **翻译质量**: ✅ 中文翻译自然准确
- **难度分级**: ✅ 按照小学年级进行分级

---
*报告由改进的AI例句生成系统自动生成*
"""

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        logger.info(f"生成报告已保存: {report_file}")

    def print_progress_summary(self, sentences: List[Dict[str, Any]], output_file: str):
        """打印进度摘要"""
        print("\n" + "="*60)
        print(f"🎉 {self.config.ip_theme}例句生成完成！")
        print("="*60)
        print(f"📊 总计生成例句: {len(sentences)} 个")
        print(f"📁 输出文件: {output_file}")

        # 显示前几个例句作为预览
        print("\n📝 例句预览:")
        for i, sentence in enumerate(sentences[:5], 1):  # 显示前5个
            print(f"{i}. {sentence['单词']}: {sentence['英文例句']}")
            print(f"   翻译: {sentence['中文翻译']}")

        if len(sentences) > 5:
            print(f"   ... 还有 {len(sentences) - 5} 个例句")

        print("\n✅ 生成完成！请查看输出文件获取完整结果。")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="改进的AI例句仿写系统")
    parser.add_argument("--ip", choices=["小猪佩奇", "疯狂动物城", "功夫熊猫"],
                       default="小猪佩奇", help="选择IP主题")
    parser.add_argument("--batch-size", type=int, default=20, help="每批处理的单词数量")
    parser.add_argument("--delay", type=float, default=2.0, help="批次间延迟时间(秒)")
    parser.add_argument("--report-file", default="ip_words_analysis_report.md",
                       help="分析报告文件路径")

    args = parser.parse_args()

    print(f"🚀 {args.ip}AI例句生成系统")
    print("="*60)
    print(f"🎯 IP主题: {args.ip}")
    print(f"📦 批次大小: {args.batch_size}")
    print(f"⏱️  批次延迟: {args.delay}秒")
    print("="*60)

    try:
        # 初始化生成器
        config = GeneratorConfig(
            batch_size=args.batch_size,
            delay_between_batches=args.delay,
            ip_theme=args.ip
        )
        generator = ImprovedSentenceGenerator(config)

        # 加载未匹配单词
        print("📚 加载未匹配单词...")
        missing_words_dict = generator.load_missing_words_from_report(args.report_file)

        if args.ip not in missing_words_dict:
            print(f"❌ 未找到{args.ip}的未匹配单词")
            return

        words = missing_words_dict[args.ip]
        print(f"✅ 成功加载 {len(words)} 个未匹配单词")

        # 开始生成
        print(f"\n🚀 开始生成{args.ip}主题例句...")
        start_time = time.time()

        sentences = await generator.generate_all_sentences(words)

        # 保存结果
        output_file = f"{args.ip}_sentences_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        generator.save_results(sentences, output_file)

        # 生成报告
        end_time = time.time()
        processing_time = end_time - start_time
        generator.generate_report(sentences, output_file, processing_time)

        # 显示完成信息
        generator.print_progress_summary(sentences, output_file)
        print(f"⏱️  总耗时: {processing_time:.1f} 秒")

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"\n❌ 生成失败: {e}")
        print("请检查日志文件 sentence_generator.log 获取详细错误信息")


if __name__ == "__main__":
    asyncio.run(main())
