# AI例句仿写和配图提示词使用指南 🎯

## 📊 系统概览

本系统为三个IP动画的未匹配小学单词生成了完整的AI例句仿写和配图提示词，帮助补充小学英语词汇学习资源。

### 🎬 覆盖的IP动画
- **功夫熊猫**: 304个未匹配单词，16个批次
- **疯狂动物城**: 227个未匹配单词，12个批次  
- **小猪佩奇**: 205个未匹配单词，11个批次

**总计**: 736个单词，39个批次

## 📁 文件结构

```
ai_prompts/
├── 功夫熊猫/
│   ├── summary.json          # 总体信息
│   ├── batch_1.md           # 第1批次提示词
│   ├── batch_2.md           # 第2批次提示词
│   └── ...                  # 共16个批次
├── 疯狂动物城/
│   ├── summary.json
│   ├── batch_1.md
│   └── ...                  # 共12个批次
└── 小猪佩奇/
    ├── summary.json
    ├── batch_1.md
    └── ...                  # 共11个批次
```

## 🎯 提示词特点

### ✅ 严格的质量控制

1. **词汇约束**: 只能使用531个小学词汇表中的单词
2. **难度分级**: 
   - Level 1 (1-2年级): 3-5个词，主谓宾结构
   - Level 2 (3-4年级): 4-7个词，现在进行时
   - Level 3 (5-6年级): 5-8个词，过去时、情态动词
3. **句长控制**: 80%的例句控制在4-6个词以内
4. **主题融合**: 每个例句都融入对应IP的角色和场景

### 🚫 明确的禁用规则

- 超出小学范围的复杂词汇
- 专业术语和抽象概念
- 复杂动词时态和从句结构

## 🔧 使用方法

### 1. 例句生成流程

1. **选择批次**: 从对应IP文件夹中选择一个批次文件
2. **复制提示词**: 复制"例句生成提示词"部分的内容
3. **输入AI工具**: 将提示词输入ChatGPT、Claude等AI工具
4. **获取结果**: AI将返回JSON格式的例句数据

**示例输出格式**:
```json
{
  "sentences": [
    {
      "word": "angry",
      "sentence": "Peppa is angry today.",
      "sentence_zh": "佩奇今天很生气。",
      "difficulty_level": "level2",
      "word_count": 4,
      "theme_integration": "使用小猪佩奇角色表达情绪"
    }
  ]
}
```

### 2. 配图生成流程

1. **获得例句**: 先通过例句生成获得具体的英文句子
2. **选择配图提示词**: 找到对应单词的配图提示词模板
3. **替换例句**: 将"[例句将由AI生成]"替换为实际例句
4. **生成图片**: 使用DALL-E、Midjourney等AI绘图工具

**配图提示词示例**:
```
Cartoon style illustration for elementary English learning, 
featuring 小猪佩奇 characters, highlighting the word 'angry', 
bright colors, child-friendly, educational, simple background, 
clear focus on the target word concept
```

## 📋 批次处理建议

### 🎯 推荐处理顺序

1. **优先级1**: 小猪佩奇（最适合小学生，205个单词）
2. **优先级2**: 疯狂动物城（中等难度，227个单词）
3. **优先级3**: 功夫熊猫（相对较难，304个单词）

### ⚡ 批量处理技巧

1. **分批处理**: 每次处理一个批次（20个单词），避免AI输出过长
2. **质量检查**: 每批次完成后检查JSON格式和内容质量
3. **数据整合**: 将多个批次的结果合并成完整的数据集

## 🔍 质量检查清单

### ✅ 例句质量检查

- [ ] 每个例句都包含目标单词
- [ ] 所有词汇都在531个小学词汇表内
- [ ] 句子长度符合难度要求
- [ ] 语法结构适合小学生
- [ ] 融入了对应IP的角色或场景
- [ ] 中文翻译准确自然
- [ ] JSON格式正确

### 🎨 配图质量检查

- [ ] 图片风格适合儿童
- [ ] 突出显示目标单词概念
- [ ] 包含对应IP的角色元素
- [ ] 背景简洁不复杂
- [ ] 色彩鲜艳有教育性

## 📈 预期成果

完成所有批次处理后，您将获得：

1. **完整的例句数据集**: 736个高质量的小学英语例句
2. **配套插图**: 每个单词对应的教学插图
3. **分级学习资源**: 按难度等级组织的学习材料
4. **主题化内容**: 融入热门IP的趣味学习内容

## 🚀 后续应用

生成的内容可用于：

- **英语学习APP**: 作为词汇学习模块
- **教学课件**: 制作PPT和教学材料
- **练习册**: 编写英语练习题
- **在线课程**: 开发互动学习内容

## 💡 优化建议

1. **个性化调整**: 根据具体需求调整提示词
2. **本地化适配**: 针对不同地区调整翻译风格
3. **难度微调**: 根据学生水平调整句子复杂度
4. **主题扩展**: 可以添加更多IP主题

---

**注意**: 本系统生成的提示词已经过精心设计和测试，建议按照指南使用以获得最佳效果。如有问题，请参考具体的批次文件中的详细说明。
