#!/usr/bin/env python3
"""
测试AI例句生成质量
只生成5个单词的例句，让用户查看效果
"""

import json
import requests
import time

def test_sentence_generation():
    """测试生成5个单词的例句"""
    
    # API配置
    api_key = "144fa109-b0a2-43f3-9d3f-88e9f3c8e650"
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 测试单词（从小猪佩奇未匹配单词中选择5个）
    test_words = ["angry", "apple", "art", "aunt", "basketball"]
    
    # 构建提示词
    prompt = f"""你是一位专业的小学英语教师，需要为以下单词创建小猪佩奇主题的英语例句。

**目标单词列表**: {', '.join(test_words)}

**小猪佩奇角色信息**:
- 主要角色: <PERSON><PERSON><PERSON> (佩奇), <PERSON> (乔治), <PERSON><PERSON> (猪妈妈), <PERSON> (猪爸爸), Grandpa <PERSON> (猪爷爷)
- 故事背景: 温馨家庭生活
- 主要主题: 家庭, 友谊, 日常生活, 学习

**严格的小学英语词汇约束**:
你只能使用531个小学词汇表中的单词，包括：a, an, and, at, be, am, is, are, big, cat, dog, eat, go, he, i, in, it, my, no, on, see, the, to, up, we, you, red, blue, yes, box, bag, car, run, sit, sun, hat, pen, about, after, always, animal, ask, because, before, come, do, find, for, from, good, have, help, here, how, know, like, look, make, new, now, old, play, right, say, she, some, take, tell, them, there, they, think, this, time, want, what, when, where, will, with, work, would, your, happy, family等531个词汇

**绝对禁止使用的词汇类型**:
- 超出小学范围的复杂词汇（如：magnificent, extraordinary, tremendous等）
- 专业术语（如：technology, environment, responsibility等）
- 复杂动词时态（如：had been doing, will have done等）

**生成要求**:
1. 每个单词生成1个例句，必须包含该单词
2. 例句必须融入小猪佩奇的角色和场景
3. 句子控制在4-7个词以内
4. 句子结构简单，适合小学生
5. 提供准确的中文翻译
6. 确保所有词汇都在小学词汇表内

**输出格式** (严格JSON格式):
```json
[
  {{
    "单词": "apple",
    "英文例句": "Peppa eats a red apple.",
    "中文翻译": "佩奇吃一个红苹果。",
    "难度等级": "level2",
    "词数": 5,
    "主题融合": "使用佩奇角色展示日常饮食"
  }}
]
```

请为所有单词生成例句，确保JSON格式正确且符合所有要求。"""

    # 构建请求数据
    data = {
        "model": "deepseek-v3-250324",
        "messages": [
            {
                "role": "system",
                "content": "你是一位专业的小学英语教师，擅长为小学生创建简单易懂的英语例句。"
            },
            {
                "role": "user", 
                "content": prompt
            }
        ],
        "max_tokens": 2000,
        "temperature": 0.7,
        "top_p": 0.9,
        "stream": False
    }
    
    print("🧪 测试AI例句生成质量")
    print("=" * 50)
    print(f"测试单词: {', '.join(test_words)}")
    print("正在生成例句...")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            if "choices" in result and result["choices"]:
                response_text = result["choices"][0]["message"]["content"]
                print("✅ API响应成功!")
                print("\n📝 AI生成的原始响应:")
                print("-" * 30)
                print(response_text)
                print("-" * 30)
                
                # 尝试解析JSON
                try:
                    # 清理响应文本
                    cleaned_response = clean_json_response(response_text)
                    parsed_data = json.loads(cleaned_response)
                    
                    print("\n🎯 解析后的例句:")
                    print("=" * 50)
                    
                    for i, sentence_data in enumerate(parsed_data, 1):
                        print(f"\n{i}. 单词: {sentence_data.get('单词', '?')}")
                        print(f"   英文: {sentence_data.get('英文例句', '?')}")
                        print(f"   中文: {sentence_data.get('中文翻译', '?')}")
                        print(f"   词数: {sentence_data.get('词数', '?')}")
                        print(f"   难度: {sentence_data.get('难度等级', '?')}")
                        print(f"   主题: {sentence_data.get('主题融合', '?')}")
                    
                    print(f"\n📊 生成统计:")
                    print(f"   成功生成: {len(parsed_data)} 个例句")
                    print(f"   平均词数: {sum(s.get('词数', 0) for s in parsed_data) / len(parsed_data):.1f}")
                    
                    # 保存测试结果
                    with open('test_sentence_result.json', 'w', encoding='utf-8') as f:
                        json.dump(parsed_data, f, ensure_ascii=False, indent=2)
                    
                    print(f"\n💾 测试结果已保存到: test_sentence_result.json")
                    
                except json.JSONDecodeError as e:
                    print(f"\n❌ JSON解析失败: {e}")
                    print("原始响应可能格式不正确")
                    
            else:
                print("❌ 响应中没有找到生成内容")
        else:
            print(f"❌ API调用失败: HTTP {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def clean_json_response(response: str) -> str:
    """清理AI响应中的markdown格式，提取纯JSON"""
    response = response.strip()
    
    # 移除markdown代码块标记
    if '```json' in response:
        start = response.find('```json') + 7
        end = response.rfind('```')
        if end > start:
            response = response[start:end]
    elif '```' in response:
        start = response.find('```') + 3
        end = response.rfind('```')
        if end > start:
            response = response[start:end]
    
    # 查找JSON数组的开始和结束
    start_bracket = response.find('[')
    end_bracket = response.rfind(']')
    
    if start_bracket != -1 and end_bracket != -1 and end_bracket > start_bracket:
        response = response[start_bracket:end_bracket + 1]
    
    return response.strip()

if __name__ == "__main__":
    test_sentence_generation()
