#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

def test_api_connection():
    """测试新的API key连接"""
    print("🔍 测试新的阿里云百炼API key...")
    
    # 检查环境变量
    api_key = os.environ.get('WUJIE_ALIYUN_API_KEY')
    if not api_key:
        print("❌ 未找到API key环境变量")
        return False
    
    print(f"✅ API Key: {api_key[:20]}...")
    
    try:
        # 导入翻译服务
        from code.external.llm_service import LlmService

        # 初始化服务
        print("🚀 初始化翻译服务...")
        llm_service = LlmService()

        if not llm_service.available:
            print("❌ 翻译服务初始化失败")
            return False

        print("✅ 翻译服务初始化成功")

        # 测试翻译功能
        print("🧪 测试翻译功能...")
        test_sentence = "Hello world"
        test_word = "hello"

        # 使用正确的翻译函数
        sys.path.insert(0, str(project_root / "code"))
        from tools.translate_utils import translate_with_llm

        result = translate_with_llm(test_sentence, test_word)

        if result and 'sentence_zh' in result:
            print(f"✅ 翻译测试成功!")
            print(f"   原句: {test_sentence}")
            print(f"   中文: {result['sentence_zh']}")
            print(f"   单词含义: {result.get('word_meaning', '未获取')}")
            return True
        else:
            print(f"❌ 翻译测试失败: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_api_connection()
    if success:
        print("\n🎉 新API key测试成功！可以重新生成数据集了。")
    else:
        print("\n💡 如果测试失败，请检查:")
        print("   1. API key是否正确")
        print("   2. 账户余额是否充足")
        print("   3. 网络连接是否正常")
