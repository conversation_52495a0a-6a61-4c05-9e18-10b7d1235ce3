#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析三个IP动画数据中各自没有匹配到的小学单词
"""

import json
import os
from collections import defaultdict

def load_primary_words(word_file_path):
    """加载小学单词表"""
    words = []
    try:
        with open(word_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                word = line.strip()
                if word:
                    words.append(word.lower())
        print(f"✅ 成功加载小学单词表，共 {len(words)} 个单词")
        return set(words)
    except Exception as e:
        print(f"❌ 加载小学单词表失败: {e}")
        return set()

def analyze_merged_data(merged_file_path):
    """分析合并数据文件"""
    try:
        with open(merged_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ 成功加载合并数据文件，共 {len(data)} 个单词")
        
        # 统计每个IP的单词
        ip_words = defaultdict(set)
        ip_names = {}
        
        for word, sentences in data.items():
            word_lower = word.lower()
            for sentence in sentences:
                if 'ip_id' in sentence:
                    ip_id = sentence['ip_id']
                    ip_words[ip_id].add(word_lower)
                    
                    # 尝试从数据中推断IP名称
                    if ip_id not in ip_names:
                        if 'IP1750926257' in ip_id:
                            ip_names[ip_id] = "功夫熊猫"
                        elif 'IP1fef9dba' in ip_id:
                            ip_names[ip_id] = "汽车总动员"
                        elif 'IP9754cf1e' in ip_id:
                            ip_names[ip_id] = "冰雪奇缘"
                        else:
                            ip_names[ip_id] = ip_id
        
        return ip_words, ip_names
        
    except Exception as e:
        print(f"❌ 加载合并数据文件失败: {e}")
        return {}, {}

def find_missing_words(primary_words, ip_words):
    """找出每个IP缺失的小学单词"""
    missing_words = {}
    
    for ip_id, matched_words in ip_words.items():
        missing = primary_words - matched_words
        missing_words[ip_id] = sorted(list(missing))
    
    return missing_words

def generate_report(primary_words, ip_words, ip_names, missing_words):
    """生成分析报告"""
    
    print("\n" + "="*80)
    print("📊 三个IP动画小学单词匹配分析报告")
    print("="*80)
    
    print(f"\n📚 小学单词表总数: {len(primary_words)} 个单词")
    
    print(f"\n🎬 发现的IP动画数据:")
    for ip_id, name in ip_names.items():
        print(f"   - {ip_id}: {name}")
    
    print(f"\n📈 各IP匹配统计:")
    for ip_id, matched_words in ip_words.items():
        name = ip_names.get(ip_id, ip_id)
        matched_count = len(matched_words)
        missing_count = len(missing_words[ip_id])
        match_rate = (matched_count / len(primary_words)) * 100
        
        print(f"\n🎭 {name} ({ip_id}):")
        print(f"   ✅ 已匹配单词: {matched_count} 个 ({match_rate:.1f}%)")
        print(f"   ❌ 未匹配单词: {missing_count} 个 ({100-match_rate:.1f}%)")
    
    # 详细的未匹配单词列表
    print(f"\n📝 详细未匹配单词列表:")
    print("-"*80)
    
    for ip_id, missing in missing_words.items():
        name = ip_names.get(ip_id, ip_id)
        print(f"\n🎭 {name} ({ip_id}) - 未匹配的 {len(missing)} 个单词:")
        
        # 按字母顺序分组显示
        current_letter = ""
        line_words = []
        
        for word in missing:
            if word[0].upper() != current_letter:
                if line_words:
                    print(f"   {current_letter}: {', '.join(line_words)}")
                current_letter = word[0].upper()
                line_words = [word]
            else:
                line_words.append(word)
                
            # 每行最多显示10个单词
            if len(line_words) >= 10:
                print(f"   {current_letter}: {', '.join(line_words)}")
                line_words = []
        
        # 打印最后一组
        if line_words:
            print(f"   {current_letter}: {', '.join(line_words)}")
    
    # 找出所有IP都缺失的单词
    all_missing = set(missing_words[list(missing_words.keys())[0]])
    for missing in missing_words.values():
        all_missing = all_missing.intersection(set(missing))
    
    if all_missing:
        print(f"\n🔍 所有IP都缺失的单词 ({len(all_missing)} 个):")
        print(f"   {', '.join(sorted(all_missing))}")
    
    # 找出只有一个IP缺失的单词
    print(f"\n🎯 各IP独有缺失的单词:")
    for ip_id, missing in missing_words.items():
        name = ip_names.get(ip_id, ip_id)
        unique_missing = set(missing)
        
        # 排除其他IP也缺失的单词
        for other_ip, other_missing in missing_words.items():
            if other_ip != ip_id:
                unique_missing = unique_missing - set(other_missing)
        
        if unique_missing:
            print(f"   {name}: {', '.join(sorted(unique_missing))} ({len(unique_missing)} 个)")
        else:
            print(f"   {name}: 无独有缺失单词")

def save_detailed_report(primary_words, ip_words, ip_names, missing_words, output_file):
    """保存详细报告到文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# 三个IP动画小学单词匹配分析报告\n\n")
            
            f.write(f"## 📊 总体统计\n\n")
            f.write(f"- **小学单词表总数**: {len(primary_words)} 个单词\n")
            f.write(f"- **IP动画数量**: {len(ip_words)} 个\n\n")
            
            f.write(f"## 🎬 IP动画信息\n\n")
            for ip_id, name in ip_names.items():
                f.write(f"- **{name}**: {ip_id}\n")
            f.write("\n")
            
            f.write(f"## 📈 匹配统计\n\n")
            for ip_id, matched_words in ip_words.items():
                name = ip_names.get(ip_id, ip_id)
                matched_count = len(matched_words)
                missing_count = len(missing_words[ip_id])
                match_rate = (matched_count / len(primary_words)) * 100
                
                f.write(f"### {name}\n")
                f.write(f"- **已匹配单词**: {matched_count} 个 ({match_rate:.1f}%)\n")
                f.write(f"- **未匹配单词**: {missing_count} 个 ({100-match_rate:.1f}%)\n\n")
            
            f.write(f"## 📝 详细未匹配单词列表\n\n")
            for ip_id, missing in missing_words.items():
                name = ip_names.get(ip_id, ip_id)
                f.write(f"### {name} - 未匹配的 {len(missing)} 个单词\n\n")
                f.write("```\n")
                f.write(", ".join(missing))
                f.write("\n```\n\n")
        
        print(f"\n💾 详细报告已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")

def main():
    """主函数"""
    # 文件路径
    merged_file = "1_merged.json"
    word_file = "data/小学单词表.txt"
    output_file = "ip_words_analysis_report.md"
    
    # 检查文件是否存在
    if not os.path.exists(merged_file):
        print(f"❌ 找不到合并数据文件: {merged_file}")
        return
    
    if not os.path.exists(word_file):
        print(f"❌ 找不到小学单词表文件: {word_file}")
        return
    
    # 加载数据
    primary_words = load_primary_words(word_file)
    if not primary_words:
        return
    
    ip_words, ip_names = analyze_merged_data(merged_file)
    if not ip_words:
        return
    
    # 分析缺失单词
    missing_words = find_missing_words(primary_words, ip_words)
    
    # 生成报告
    generate_report(primary_words, ip_words, ip_names, missing_words)
    
    # 保存详细报告
    save_detailed_report(primary_words, ip_words, ip_names, missing_words, output_file)

if __name__ == "__main__":
    main()
