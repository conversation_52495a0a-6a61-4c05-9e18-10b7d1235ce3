# 🎬 简易视频生成器使用示例

## 快速开始

### 1. 启动Web界面
```bash
python3 start_web_interface.py
```

### 2. 访问简易生成器
打开浏览器访问：http://localhost:3000/simple-generator

### 3. 准备章节数据

找到你要处理的章节目录，例如：
```
output/novel_step2/大唐太子：开局硬刚李世民/chapter1_风起大秦公子华/storyboards.json
```

打开这个JSON文件，复制全部内容。

### 4. 填写表单

在Web界面中：
- **章节名称**: 输入 `chapter1_风起大秦公子华`
- **JSON数据**: 粘贴刚才复制的JSON内容
- **并行线程数**: 保持默认60（或根据你的CPU调整）

### 5. 生成视频

点击"生成视频"按钮，系统会：
1. 验证JSON格式
2. 创建处理任务
3. 开始视频剪辑
4. 显示实时进度
5. 完成后提供下载链接

## 示例JSON数据格式

你的storyboards.json应该包含类似这样的数据：

```json
[
  {
    "chapter": 1,
    "story_board": "秦风，一个在洛杉矶打拼的华人精英...",
    "characters": [...],
    "scene": "室外",
    "image_prompt": "中景，一个先是眼神充满自信...",
    "image_url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/...",
    "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/...",
    "audio_duration": 16.725333,
    "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/...",
    "story_board_id": 1,
    "subtitle": [
      {
        "start_time": 0.0,
        "end_time": 3.2,
        "text": "秦风，一个在洛杉矶打拼的华人精英"
      }
    ]
  },
  {
    "chapter": 1,
    "story_board": "下一个分镜内容...",
    // ... 更多分镜数据
  }
]
```

## 处理流程

1. **JSON验证**: 系统会验证你输入的JSON格式是否正确
2. **数据解析**: 提取视频URL、图片URL、音频URL和字幕信息
3. **素材处理**: 下载和处理所有素材文件
4. **时间轴构建**: 根据音频时长构建视频时间轴
5. **视频合成**: 使用阿里云API进行视频剪辑
6. **结果输出**: 生成最终视频并提供下载链接

## 常见问题

### Q: JSON格式错误怎么办？
A: 确保复制的是完整的JSON内容，可以使用在线JSON验证工具检查格式。

### Q: 处理失败怎么办？
A: 检查错误信息，常见原因：
- JSON格式不正确
- 素材URL无效
- 网络连接问题
- 服务器资源不足

### Q: 可以同时处理多个章节吗？
A: 简易生成器一次只能处理一个章节。如需批量处理，请使用"并行视频处理器"。

### Q: 生成的视频在哪里？
A: 视频会保存在 `output/novel_step3/章节名称/` 目录下，同时会提供在线下载链接。

## 技术说明

这个Web界面实际上是对你原有命令行工具的封装：

**原命令行**:
```bash
python src/video_synthesis/parallel_video_processor.py \
  --novel_dir output/novel_step2/大唐太子：开局硬刚李世民 \
  --output_dir output/novel_step3 \
  --max_workers 60
```

**Web界面做的事情**:
1. 接收你粘贴的JSON数据
2. 临时创建章节目录和storyboards.json文件
3. 调用 `AutoVideoEditor` 进行视频剪辑
4. 返回处理结果

所以功能完全一致，只是使用方式更加友好！

## 优势

✅ **无需命令行**: 图形界面操作，更加直观
✅ **即时反馈**: 实时显示处理进度和状态
✅ **错误提示**: 清晰的错误信息和解决建议
✅ **结果管理**: 自动保存和提供下载链接
✅ **跨平台**: 任何有浏览器的设备都可以使用

开始享受可视化的视频剪辑体验吧！🎉
