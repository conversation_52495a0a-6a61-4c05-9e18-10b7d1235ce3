#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "code"))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

def regenerate_dataset():
    """重新生成带有正确中文翻译的数据集"""
    print("🚀 开始重新生成小学词汇数据集...")
    print("📋 参数设置:")
    print("   - IP ID: IP352a1921")
    print("   - 年级: 小学")
    print("   - 最大例句数: 7")
    
    try:
        # 导入必要的模块
        from tools.word_dataset_generator import WordDatasetGenerator
        
        # 创建生成器实例
        generator = WordDatasetGenerator()
        
        # 设置参数
        ip_id = "IP352a1921"
        grade = "primary"
        max_sentences = 7
        
        print(f"\n🔄 开始处理 {ip_id} 的{grade}词汇...")
        
        # 生成数据集
        result = generator.generate_dataset(
            ip_id=ip_id,
            grade=grade,
            max_sentences=max_sentences
        )
        
        if result:
            print("✅ 数据集生成成功！")
            print(f"📁 输出文件: output/{ip_id}/word_dataset_{grade}.json")
            print(f"📊 报告文件: output/{ip_id}/word_dataset_{grade}_report.md")
            
            # 显示统计信息
            if 'metadata' in result:
                metadata = result['metadata']
                print(f"\n📈 统计信息:")
                print(f"   - 总单词数: {metadata.get('total_words', 0)}")
                print(f"   - 匹配单词数: {metadata.get('matched_words', 0)}")
                print(f"   - 生成例句数: {metadata.get('total_sentences', 0)}")
            
            return True
        else:
            print("❌ 数据集生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 生成过程中出现错误: {str(e)}")
        return False

if __name__ == "__main__":
    print("🎯 重新生成词汇数据集")
    print("=" * 50)
    
    success = regenerate_dataset()
    
    if success:
        print("\n🎉 任务完成！现在数据集应该包含正确的中文翻译了。")
        print("\n💡 你可以检查以下文件:")
        print("   - output/IP352a1921/word_dataset_primary.json")
        print("   - output/IP352a1921/word_dataset_primary_report.md")
    else:
        print("\n💔 任务失败，请检查错误信息并重试。")
