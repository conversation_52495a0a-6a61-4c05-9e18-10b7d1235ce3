#!/usr/bin/env python3
"""
测试模式二（视频+字幕处理）功能
"""

import os
import sys
from pathlib import Path

# 添加代码路径
sys.path.append('code')

from service.ui_processor import UIProcessor

def test_mode2_processing():
    """测试模式二处理功能"""
    
    # 设置路径
    project_root = "/Users/<USER>/auto-video-extract"
    paw_dir = os.path.join(project_root, "output/videos/PAW")
    
    # 检查PAW目录中的文件
    video_file = None
    lrc_file = None
    
    for file in os.listdir(paw_dir):
        if file.endswith('.mp4'):
            video_file = os.path.join(paw_dir, file)
        elif file.endswith('.lrc'):
            lrc_file = os.path.join(paw_dir, file)
    
    if not video_file or not lrc_file:
        print("❌ PAW目录中缺少必要的文件")
        print(f"视频文件: {video_file}")
        print(f"字幕文件: {lrc_file}")
        return False
    
    print(f"✅ 找到输入文件:")
    print(f"   视频: {os.path.basename(video_file)}")
    print(f"   字幕: {os.path.basename(lrc_file)}")
    
    # 创建UI处理器
    output_dir = os.path.join(project_root, "output")
    ui_processor = UIProcessor(project_root, output_dir)
    
    # 设置IP ID
    ip_id = "PAW_TEST"
    
    print(f"\n🔄 开始模式二处理...")
    print(f"   IP ID: {ip_id}")
    
    # 创建简单的日志记录器
    class SimpleLogger:
        def write(self, text):
            # 直接写到标准输出，避免递归
            import sys
            sys.__stdout__.write(text)
        def flush(self):
            import sys
            sys.__stdout__.flush()
    
    # 创建占位符类
    class SimplePlaceholder:
        def __init__(self, name):
            self.name = name
        def progress(self, value):
            print(f"[{self.name}] 进度: {value*100:.1f}%")
        def info(self, text):
            print(f"[{self.name}] INFO: {text}")
        def success(self, text):
            print(f"[{self.name}] SUCCESS: {text}")
        def error(self, text):
            print(f"[{self.name}] ERROR: {text}")
        def markdown(self, text):
            print(f"[{self.name}] RESULT:\n{text}")
    
    logger = SimpleLogger()
    progress_placeholder = SimplePlaceholder("PROGRESS")
    status_placeholder = SimplePlaceholder("STATUS")
    result_placeholder = SimplePlaceholder("RESULT")
    
    try:
        # 调用模式二处理函数
        success = ui_processor.process_video_with_subtitle(
            video_path=video_file,
            subtitle_path=lrc_file,
            selected_ip=ip_id,
            logger=logger,
            progress_placeholder=progress_placeholder,
            status_placeholder=status_placeholder,
            result_placeholder=result_placeholder,
            audio_buffer=0.8,
            start_buffer=0.1
        )
        
        if success:
            print("\n✅ 模式二处理完成!")
            return True
        else:
            print("\n❌ 模式二处理失败!")
            return False
            
    except Exception as e:
        print(f"\n❌ 处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试模式二（视频+字幕处理）功能\n")
    
    # 设置环境变量
    os.environ['WUJIE_ALIYUN_API_KEY'] = 'sk-c124f6352fb94e57aad1ab929184f278'
    
    success = test_mode2_processing()
    
    if success:
        print("\n🎉 测试完成! 请检查输出目录中的结果。")
    else:
        print("\n💥 测试失败!")
