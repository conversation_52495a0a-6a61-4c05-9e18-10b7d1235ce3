#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI例句仿写和配图提示词生成系统
专门为未匹配的小学单词生成高质量的例句和配图提示词
"""

import json
import os
from typing import List, Dict, Any

class SentenceGenerationPrompts:
    """AI例句生成提示词类"""
    
    def __init__(self):
        # 加载小学单词表
        self.primary_words = self._load_primary_words()
        
        # 定义IP角色信息
        self.ip_characters = {
            "功夫熊猫": {
                "main_characters": ["<PERSON> (熊猫阿宝)", "Master Shifu (师父)", "Tigress (娇虎)", "Viper (蛇蝰)", "<PERSON> (仙鹤)"],
                "setting": "中国古代功夫世界",
                "themes": ["功夫训练", "友谊", "勇气", "成长"]
            },
            "疯狂动物城": {
                "main_characters": ["<PERSON> (兔子朱迪)", "<PERSON> (狐狸尼克)", "Chief <PERSON><PERSON> (水牛局长)", "Flash (树懒闪电)"],
                "setting": "现代动物城市",
                "themes": ["友谊", "正义", "梦想", "合作"]
            },
            "小猪佩奇": {
                "main_characters": ["Peppa (佩奇)", "George (乔治)", "Mummy Pig (猪妈妈)", "Daddy Pig (猪爸爸)", "Grandpa Pig (猪爷爷)"],
                "setting": "温馨家庭生活",
                "themes": ["家庭", "友谊", "日常生活", "学习"]
            }
        }
        
        # 难度等级定义
        self.difficulty_levels = {
            "level1": {  # 1-2年级
                "sentence_length": "3-5个词",
                "grammar": "主谓宾结构，现在时",
                "examples": ["I like apples.", "The cat is big.", "We play games."]
            },
            "level2": {  # 3-4年级  
                "sentence_length": "4-7个词",
                "grammar": "现在进行时，简单疑问句",
                "examples": ["She is reading a book.", "Do you like music?", "They are playing football."]
            },
            "level3": {  # 5-6年级
                "sentence_length": "5-8个词",
                "grammar": "过去时，情态动词，复合句",
                "examples": ["I went to school yesterday.", "You should eat vegetables.", "He can swim very well."]
            }
        }
    
    def _load_primary_words(self) -> set:
        """加载小学单词表"""
        words = set()
        try:
            with open('data/小学单词表.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    word = line.strip().lower()
                    if word:
                        words.add(word)
        except Exception as e:
            print(f"加载小学单词表失败: {e}")
        return words
    
    def get_word_difficulty_level(self, word: str) -> str:
        """根据单词特征判断难度等级"""
        word = word.lower()
        
        # Level 1: 基础高频词汇
        level1_words = {
            'a', 'an', 'and', 'at', 'be', 'am', 'is', 'are', 'big', 'cat', 'dog', 'eat', 'go', 
            'he', 'i', 'in', 'it', 'my', 'no', 'on', 'see', 'the', 'to', 'up', 'we', 'you'
        }
        
        # Level 2: 中等词汇
        level2_words = {
            'about', 'after', 'always', 'animal', 'ask', 'because', 'before', 'come', 'do',
            'find', 'for', 'from', 'good', 'have', 'help', 'here', 'how', 'know', 'like',
            'look', 'make', 'new', 'now', 'old', 'play', 'right', 'say', 'she', 'some',
            'take', 'tell', 'them', 'there', 'they', 'think', 'this', 'time', 'want', 'what',
            'when', 'where', 'will', 'with', 'work', 'would', 'your'
        }
        
        if word in level1_words or len(word) <= 3:
            return "level1"
        elif word in level2_words or len(word) <= 5:
            return "level2"
        else:
            return "level3"
    
    def generate_sentence_prompt(self, words: List[str], ip_name: str) -> str:
        """生成例句仿写提示词"""
        
        # 获取角色信息
        characters_info = self.ip_characters.get(ip_name, {})
        
        # 将单词按难度分组
        words_by_level = {"level1": [], "level2": [], "level3": []}
        for word in words:
            level = self.get_word_difficulty_level(word)
            words_by_level[level].append(word)
        
        # 生成小学词汇约束字符串
        allowed_words_str = ", ".join(sorted(list(self.primary_words)))
        
        prompt = f"""你是一位专业的小学英语教师，需要为以下单词创建{ip_name}主题的英语例句。

**目标单词列表**: {', '.join(words)}

**{ip_name}角色信息**:
- 主要角色: {', '.join(characters_info.get('main_characters', []))}
- 故事背景: {characters_info.get('setting', '动画世界')}
- 主要主题: {', '.join(characters_info.get('themes', []))}

**严格的小学英语词汇约束**:
你只能使用以下531个小学词汇表中的单词：
{allowed_words_str}

**绝对禁止使用的词汇类型**:
- 超出小学范围的复杂词汇（如：magnificent, extraordinary, tremendous等）
- 专业术语（如：technology, environment, responsibility等）
- 抽象概念词（如：philosophy, psychology, democracy等）
- 复杂动词时态（如：had been doing, will have done等）

**分级难度要求**:

1. **1-2年级词汇** ({', '.join(words_by_level['level1'])}):
   - 句长：3-5个词
   - 语法：主谓宾结构，一般现在时
   - 示例：Po is big. / I like cats. / We eat apples.

2. **3-4年级词汇** ({', '.join(words_by_level['level2'])}):
   - 句长：4-7个词  
   - 语法：现在进行时，简单疑问句
   - 示例：Judy is helping Nick. / Do you like music?

3. **5-6年级词汇** ({', '.join(words_by_level['level3'])}):
   - 句长：5-8个词
   - 语法：一般过去时，情态动词can/should
   - 示例：Po learned kung fu yesterday. / You should be careful.

**生成要求**:
1. 每个单词生成1个例句，必须包含该单词
2. 例句必须融入{ip_name}的角色和场景
3. 80%的例句控制在4-6个词以内
4. 句子结构简单，避免复杂从句
5. 提供准确的中文翻译
6. 确保所有词汇都在531个小学词汇表内

**输出格式** (严格JSON格式):
{{
  "sentences": [
    {{
      "word": "单词",
      "sentence": "英文例句",
      "sentence_zh": "中文翻译",
      "difficulty_level": "level1/level2/level3",
      "word_count": 句子词数,
      "theme_integration": "如何融入{ip_name}主题的说明"
    }}
  ]
}}

**质量检查清单**:
- ✅ 每个例句都包含目标单词
- ✅ 所有词汇都在531个小学词汇表内
- ✅ 句子长度符合难度要求
- ✅ 语法结构适合小学生
- ✅ 融入了{ip_name}的角色或场景
- ✅ 中文翻译准确自然

请为所有单词生成例句，确保JSON格式正确且符合所有要求。"""

        return prompt
    
    def generate_image_prompt(self, word: str, sentence: str, ip_name: str) -> str:
        """生成配图提示词"""
        
        characters_info = self.ip_characters.get(ip_name, {})
        
        prompt = f"""为小学英语学习生成插图提示词

**目标单词**: {word}
**例句**: {sentence}
**动画主题**: {ip_name}

**角色风格**: {characters_info.get('setting', '动画风格')}
**主要角色**: {', '.join(characters_info.get('main_characters', [])[:2])}

**图片要求**:
1. **风格**: 卡通动画风格，色彩鲜艳，适合儿童
2. **主体**: 突出显示单词"{word}"相关的物体或动作
3. **角色**: 包含{ip_name}中的角色
4. **场景**: 简洁明了，不要过于复杂
5. **教育性**: 帮助小学生理解单词含义

**配图提示词**:
"Cartoon style illustration for elementary English learning, featuring {ip_name} characters, highlighting the word '{word}', bright colors, child-friendly, educational, simple background, clear focus on the target word concept"

**中文描述**: 
{ip_name}风格的卡通插图，突出展示单词"{word}"，色彩鲜艳，适合小学生英语学习，背景简洁，重点突出目标单词概念。"""

        return prompt

    def generate_batch_prompts_for_missing_words(self, missing_words_file: str = "ip_words_analysis_report.md") -> Dict[str, Any]:
        """为所有IP的未匹配单词生成批量提示词"""

        # 从分析报告中提取未匹配单词
        missing_words_by_ip = self._extract_missing_words_from_report(missing_words_file)

        batch_prompts = {}

        for ip_name, missing_words in missing_words_by_ip.items():
            print(f"\n🎬 为 {ip_name} 生成提示词，共 {len(missing_words)} 个单词")

            # 分批处理（每批20个单词）
            batch_size = 20
            batches = [missing_words[i:i+batch_size] for i in range(0, len(missing_words), batch_size)]

            batch_prompts[ip_name] = {
                "total_words": len(missing_words),
                "batches": []
            }

            for i, batch_words in enumerate(batches, 1):
                sentence_prompt = self.generate_sentence_prompt(batch_words, ip_name)

                batch_info = {
                    "batch_number": i,
                    "words": batch_words,
                    "word_count": len(batch_words),
                    "sentence_prompt": sentence_prompt,
                    "image_prompts": []
                }

                # 为每个单词生成配图提示词模板
                for word in batch_words:
                    image_prompt_template = self.generate_image_prompt(word, f"[例句将由AI生成]", ip_name)
                    batch_info["image_prompts"].append({
                        "word": word,
                        "prompt_template": image_prompt_template
                    })

                batch_prompts[ip_name]["batches"].append(batch_info)

        return batch_prompts

    def _extract_missing_words_from_report(self, report_file: str) -> Dict[str, List[str]]:
        """从分析报告中提取未匹配单词"""
        missing_words = {}

        try:
            with open(report_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析报告内容
            lines = content.split('\n')
            current_ip = None
            in_code_block = False

            for i, line in enumerate(lines):
                # 检测IP标题
                if "### 功夫熊猫 - 未匹配的" in line:
                    current_ip = "功夫熊猫"
                    in_code_block = False
                elif "### 疯狂动物城 - 未匹配的" in line:
                    current_ip = "疯狂动物城"
                    in_code_block = False
                elif "### 小猪佩奇 - 未匹配的" in line:
                    current_ip = "小猪佩奇"
                    in_code_block = False

                # 检测代码块开始
                elif line.strip() == "```" and current_ip and not in_code_block:
                    in_code_block = True

                # 提取单词列表（在代码块内的非空行）
                elif in_code_block and current_ip and line.strip() and line.strip() != "```":
                    words_line = line.strip()
                    if words_line:
                        words = [word.strip() for word in words_line.split(',') if word.strip()]
                        missing_words[current_ip] = words
                        in_code_block = False
                        current_ip = None

                # 检测代码块结束
                elif line.strip() == "```" and in_code_block:
                    in_code_block = False

        except Exception as e:
            print(f"解析报告文件失败: {e}")
            # 使用默认的测试数据
            missing_words = {
                "功夫熊猫": ["angry", "art", "aunt", "basketball", "bee"],
                "疯狂动物城": ["ball", "begin", "cook", "easy", "favorite"],
                "小猪佩奇": ["answer", "ask", "bear", "china", "cut"]
            }

        return missing_words

    def save_prompts_to_files(self, batch_prompts: Dict[str, Any], output_dir: str = "ai_prompts"):
        """保存提示词到文件"""
        os.makedirs(output_dir, exist_ok=True)

        for ip_name, ip_data in batch_prompts.items():
            ip_dir = os.path.join(output_dir, ip_name.replace(" ", "_"))
            os.makedirs(ip_dir, exist_ok=True)

            # 保存总体信息
            summary = {
                "ip_name": ip_name,
                "total_words": ip_data["total_words"],
                "total_batches": len(ip_data["batches"]),
                "batch_info": [
                    {
                        "batch_number": batch["batch_number"],
                        "word_count": batch["word_count"],
                        "words": batch["words"]
                    }
                    for batch in ip_data["batches"]
                ]
            }

            with open(os.path.join(ip_dir, "summary.json"), 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)

            # 保存每个批次的提示词
            for batch in ip_data["batches"]:
                batch_file = os.path.join(ip_dir, f"batch_{batch['batch_number']}.md")

                with open(batch_file, 'w', encoding='utf-8') as f:
                    f.write(f"# {ip_name} - 批次 {batch['batch_number']}\n\n")
                    f.write(f"**单词数量**: {batch['word_count']}\n")
                    f.write(f"**目标单词**: {', '.join(batch['words'])}\n\n")
                    f.write("## 例句生成提示词\n\n")
                    f.write("```\n")
                    f.write(batch['sentence_prompt'])
                    f.write("\n```\n\n")
                    f.write("## 配图提示词模板\n\n")

                    for img_prompt in batch['image_prompts']:
                        f.write(f"### {img_prompt['word']}\n\n")
                        f.write("```\n")
                        f.write(img_prompt['prompt_template'])
                        f.write("\n```\n\n")

        print(f"\n💾 所有提示词已保存到: {output_dir}")

def main():
    """主函数 - 生成完整的提示词系统"""
    generator = SentenceGenerationPrompts()

    print("🚀 开始生成AI例句仿写和配图提示词系统...")

    # 生成批量提示词
    batch_prompts = generator.generate_batch_prompts_for_missing_words()

    # 保存到文件
    generator.save_prompts_to_files(batch_prompts)

    print("\n✅ 提示词系统生成完成！")
    print("\n📊 生成统计:")
    for ip_name, ip_data in batch_prompts.items():
        print(f"   {ip_name}: {ip_data['total_words']} 个单词，{len(ip_data['batches'])} 个批次")

if __name__ == "__main__":
    main()
