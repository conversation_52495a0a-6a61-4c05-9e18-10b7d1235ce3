2025-08-05 23:06:07,709 - INFO - 成功加载小学词汇表，共 531 个单词
2025-08-05 23:06:07,710 - INFO - 将 205 个单词分为 21 批，每批最多 10 个
2025-08-05 23:06:07,710 - INFO - 开始处理第 1/21 批，包含 10 个单词
2025-08-05 23:06:08,000 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:08,001 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-69b3ee5c-ed30-9de9-8371-be40e638adb2', 'request_id': '69b3ee5c-ed30-9de9-8371-be40e638adb2'}
2025-08-05 23:06:12,079 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:12,080 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-52ede424-3108-9fa4-88ff-132f4fd0b0c0', 'request_id': '52ede424-3108-9fa4-88ff-132f4fd0b0c0'}
2025-08-05 23:06:16,159 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:16,160 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-e0ab93a0-344b-9460-a8f6-ea7d0364e702', 'request_id': 'e0ab93a0-344b-9460-a8f6-ea7d0364e702'}
2025-08-05 23:06:16,161 - ERROR - 第 1 批处理失败: RetryError[<Future at 0x1198bb550 state=finished raised BadRequestError>]
2025-08-05 23:06:16,161 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:06:16,161 - INFO - 开始处理第 2/21 批，包含 10 个单词
2025-08-05 23:06:16,322 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:16,324 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-b35b00cb-524e-93ed-a3c9-a7a0a8e49b71', 'request_id': 'b35b00cb-524e-93ed-a3c9-a7a0a8e49b71'}
2025-08-05 23:06:20,488 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:20,489 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-8de3e0d0-7b0f-92ef-add5-22a30fb471ca', 'request_id': '8de3e0d0-7b0f-92ef-add5-22a30fb471ca'}
2025-08-05 23:06:24,632 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:24,634 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-70a87ac3-f07a-9f6b-9ab0-a6aaa2ed6fd9', 'request_id': '70a87ac3-f07a-9f6b-9ab0-a6aaa2ed6fd9'}
2025-08-05 23:06:24,635 - ERROR - 第 2 批处理失败: RetryError[<Future at 0x119e294b0 state=finished raised BadRequestError>]
2025-08-05 23:06:24,635 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:06:24,635 - INFO - 开始处理第 3/21 批，包含 10 个单词
2025-08-05 23:06:24,873 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:24,874 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-b59de623-877b-94ea-b8c1-f3947b49e301', 'request_id': 'b59de623-877b-94ea-b8c1-f3947b49e301'}
2025-08-05 23:06:28,959 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:28,959 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-38c1ebae-b44a-9e16-84af-06f7ca1a8a5f', 'request_id': '38c1ebae-b44a-9e16-84af-06f7ca1a8a5f'}
2025-08-05 23:06:33,101 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:33,102 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-ca99adeb-6e69-986f-a790-d6c4f722f3c5', 'request_id': 'ca99adeb-6e69-986f-a790-d6c4f722f3c5'}
2025-08-05 23:06:33,103 - ERROR - 第 3 批处理失败: RetryError[<Future at 0x119e4c610 state=finished raised BadRequestError>]
2025-08-05 23:06:33,103 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:06:33,103 - INFO - 开始处理第 4/21 批，包含 10 个单词
2025-08-05 23:06:33,191 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:33,193 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-fd417bcf-8b04-96b4-981b-************', 'request_id': 'fd417bcf-8b04-96b4-981b-************'}
2025-08-05 23:06:37,359 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:37,360 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-11e517d6-f5a0-985c-b572-c549a9785f9f', 'request_id': '11e517d6-f5a0-985c-b572-c549a9785f9f'}
2025-08-05 23:06:41,520 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:41,521 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-54e8c073-063c-97c9-b35f-18e59dfb9e2a', 'request_id': '54e8c073-063c-97c9-b35f-18e59dfb9e2a'}
2025-08-05 23:06:41,522 - ERROR - 第 4 批处理失败: RetryError[<Future at 0x119e2bc40 state=finished raised BadRequestError>]
2025-08-05 23:06:41,522 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:06:41,522 - INFO - 开始处理第 5/21 批，包含 10 个单词
2025-08-05 23:06:41,679 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:41,680 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-a806c342-0088-98cd-bf2c-c73079bab5f1', 'request_id': 'a806c342-0088-98cd-bf2c-c73079bab5f1'}
2025-08-05 23:06:45,841 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:45,842 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-4a253455-d834-9fa2-ab98-bfe2e748cc29', 'request_id': '4a253455-d834-9fa2-ab98-bfe2e748cc29'}
2025-08-05 23:06:50,000 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:50,002 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-375f7796-1117-99bc-b503-eeaaad6748c6', 'request_id': '375f7796-1117-99bc-b503-eeaaad6748c6'}
2025-08-05 23:06:50,002 - ERROR - 第 5 批处理失败: RetryError[<Future at 0x119e6ff70 state=finished raised BadRequestError>]
2025-08-05 23:06:50,002 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:06:50,003 - INFO - 开始处理第 6/21 批，包含 10 个单词
2025-08-05 23:06:50,141 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:50,142 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-6931b2f2-4d7f-9015-abde-f8a74892e006', 'request_id': '6931b2f2-4d7f-9015-abde-f8a74892e006'}
2025-08-05 23:06:54,242 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:54,243 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-c5db994a-5c61-948d-bee7-1d876871bf58', 'request_id': 'c5db994a-5c61-948d-bee7-1d876871bf58'}
2025-08-05 23:06:58,398 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:58,399 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-77a9254b-25e8-939f-92a0-8d991e1e1706', 'request_id': '77a9254b-25e8-939f-92a0-8d991e1e1706'}
2025-08-05 23:06:58,399 - ERROR - 第 6 批处理失败: RetryError[<Future at 0x119e64850 state=finished raised BadRequestError>]
2025-08-05 23:06:58,399 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:06:58,399 - INFO - 开始处理第 7/21 批，包含 10 个单词
2025-08-05 23:06:58,560 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:06:58,561 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-c3e8506d-76dc-94af-aa8f-18372f153aa0', 'request_id': 'c3e8506d-76dc-94af-aa8f-18372f153aa0'}
2025-08-05 23:07:02,706 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:02,708 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-f11a40f4-68dd-9f76-9610-a6e16ee38d97', 'request_id': 'f11a40f4-68dd-9f76-9610-a6e16ee38d97'}
2025-08-05 23:07:06,786 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:06,787 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-f96db1cd-a7e2-9bbb-ac51-8e1e0a86b049', 'request_id': 'f96db1cd-a7e2-9bbb-ac51-8e1e0a86b049'}
2025-08-05 23:07:06,788 - ERROR - 第 7 批处理失败: RetryError[<Future at 0x119e8b490 state=finished raised BadRequestError>]
2025-08-05 23:07:06,788 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:07:06,788 - INFO - 开始处理第 8/21 批，包含 10 个单词
2025-08-05 23:07:06,881 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:06,882 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-598b9a92-9cfe-9aa9-a7b8-a7bddf737477', 'request_id': '598b9a92-9cfe-9aa9-a7b8-a7bddf737477'}
2025-08-05 23:07:11,039 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:11,040 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-bed8cf9d-9e51-944e-bc5e-2c5e35835443', 'request_id': 'bed8cf9d-9e51-944e-bc5e-2c5e35835443'}
2025-08-05 23:07:15,194 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:15,195 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-9ce1f745-6a8f-9cc7-a0cc-06552a8bf230', 'request_id': '9ce1f745-6a8f-9cc7-a0cc-06552a8bf230'}
2025-08-05 23:07:15,195 - ERROR - 第 8 批处理失败: RetryError[<Future at 0x119e8b130 state=finished raised BadRequestError>]
2025-08-05 23:07:15,196 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:07:15,196 - INFO - 开始处理第 9/21 批，包含 10 个单词
2025-08-05 23:07:15,364 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:15,366 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-96cc3e12-7880-974d-86fc-f1f650872b9a', 'request_id': '96cc3e12-7880-974d-86fc-f1f650872b9a'}
2025-08-05 23:07:19,519 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:19,520 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-1d9c3766-eabb-9420-8921-fcdb00ce642a', 'request_id': '1d9c3766-eabb-9420-8921-fcdb00ce642a'}
2025-08-05 23:07:23,679 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:23,680 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-4bfb4713-8295-95d9-ab6f-5844449d3c32', 'request_id': '4bfb4713-8295-95d9-ab6f-5844449d3c32'}
2025-08-05 23:07:23,681 - ERROR - 第 9 批处理失败: RetryError[<Future at 0x119ea1990 state=finished raised BadRequestError>]
2025-08-05 23:07:23,681 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:07:23,681 - INFO - 开始处理第 10/21 批，包含 10 个单词
2025-08-05 23:07:23,827 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:23,831 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-3f4c2977-6fe7-9fe1-8eab-0e4c2f5756d9', 'request_id': '3f4c2977-6fe7-9fe1-8eab-0e4c2f5756d9'}
2025-08-05 23:07:27,923 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:27,924 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-2b998ceb-ee80-9973-acc2-9a5223eb5797', 'request_id': '2b998ceb-ee80-9973-acc2-9a5223eb5797'}
2025-08-05 23:07:32,250 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:32,251 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-182926da-a897-9c35-8e92-44b2360a0727', 'request_id': '182926da-a897-9c35-8e92-44b2360a0727'}
2025-08-05 23:07:32,251 - ERROR - 第 10 批处理失败: RetryError[<Future at 0x119ea0520 state=finished raised BadRequestError>]
2025-08-05 23:07:32,252 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:07:32,252 - INFO - 开始处理第 11/21 批，包含 10 个单词
2025-08-05 23:07:32,411 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:32,413 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-5554b7c4-88b6-9d55-afb7-1fb9af8e1fd8', 'request_id': '5554b7c4-88b6-9d55-afb7-1fb9af8e1fd8'}
2025-08-05 23:07:36,562 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:36,563 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-6aea223d-f1a1-9b18-b1a3-5c815e1da015', 'request_id': '6aea223d-f1a1-9b18-b1a3-5c815e1da015'}
2025-08-05 23:07:40,720 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:40,721 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-2b0ec20e-f889-9ae8-b0f7-177bd27d14d2', 'request_id': '2b0ec20e-f889-9ae8-b0f7-177bd27d14d2'}
2025-08-05 23:07:40,722 - ERROR - 第 11 批处理失败: RetryError[<Future at 0x119e7d030 state=finished raised BadRequestError>]
2025-08-05 23:07:40,722 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:07:40,722 - INFO - 开始处理第 12/21 批，包含 10 个单词
2025-08-05 23:07:40,834 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:40,835 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-0a8dda16-3fd2-9e5b-96f1-08a312da3f26', 'request_id': '0a8dda16-3fd2-9e5b-96f1-08a312da3f26'}
2025-08-05 23:07:44,980 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:44,982 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-ebe49292-5718-946e-a449-f138b0c6f77d', 'request_id': 'ebe49292-5718-946e-a449-f138b0c6f77d'}
2025-08-05 23:07:49,106 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:49,107 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-51a3460f-3b14-9204-a67a-9423e29a9b26', 'request_id': '51a3460f-3b14-9204-a67a-9423e29a9b26'}
2025-08-05 23:07:49,108 - ERROR - 第 12 批处理失败: RetryError[<Future at 0x119e67340 state=finished raised BadRequestError>]
2025-08-05 23:07:49,108 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:07:49,108 - INFO - 开始处理第 13/21 批，包含 10 个单词
2025-08-05 23:07:49,193 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:49,194 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-d55471f5-c03e-9715-ad91-57a486bde323', 'request_id': 'd55471f5-c03e-9715-ad91-57a486bde323'}
2025-08-05 23:07:53,289 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:53,290 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-51f6bdc5-5c07-9117-a2fe-e17a2e0e6b15', 'request_id': '51f6bdc5-5c07-9117-a2fe-e17a2e0e6b15'}
2025-08-05 23:07:57,424 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:57,425 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-c19fd2bf-7909-99dc-a25f-931246d8cabe', 'request_id': 'c19fd2bf-7909-99dc-a25f-931246d8cabe'}
2025-08-05 23:07:57,426 - ERROR - 第 13 批处理失败: RetryError[<Future at 0x119e6c6a0 state=finished raised BadRequestError>]
2025-08-05 23:07:57,426 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:07:57,426 - INFO - 开始处理第 14/21 批，包含 10 个单词
2025-08-05 23:07:57,519 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:07:57,520 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-e0bbf74d-ae2d-9536-9e88-be26ba465432', 'request_id': 'e0bbf74d-ae2d-9536-9e88-be26ba465432'}
2025-08-05 23:08:01,682 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:01,684 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-2da536e7-d9e5-98b5-915b-216411154f37', 'request_id': '2da536e7-d9e5-98b5-915b-216411154f37'}
2025-08-05 23:08:05,883 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:05,884 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-bac061e7-b478-9c22-b557-2b582290a81e', 'request_id': 'bac061e7-b478-9c22-b557-2b582290a81e'}
2025-08-05 23:08:05,884 - ERROR - 第 14 批处理失败: RetryError[<Future at 0x119e8ae60 state=finished raised BadRequestError>]
2025-08-05 23:08:05,884 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:08:05,885 - INFO - 开始处理第 15/21 批，包含 10 个单词
2025-08-05 23:08:06,008 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:06,010 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-ef93588c-593f-9729-889d-b847e2a073b4', 'request_id': 'ef93588c-593f-9729-889d-b847e2a073b4'}
2025-08-05 23:08:10,160 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:10,161 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-3cc72c24-648f-9fac-9b69-10c50095afae', 'request_id': '3cc72c24-648f-9fac-9b69-10c50095afae'}
2025-08-05 23:08:14,310 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:14,310 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-c742af61-7f16-9723-a3df-6fa46dba80cf', 'request_id': 'c742af61-7f16-9723-a3df-6fa46dba80cf'}
2025-08-05 23:08:14,310 - ERROR - 第 15 批处理失败: RetryError[<Future at 0x1198bb1c0 state=finished raised BadRequestError>]
2025-08-05 23:08:14,311 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:08:14,311 - INFO - 开始处理第 16/21 批，包含 10 个单词
2025-08-05 23:08:14,386 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:14,386 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-a86d32bb-dd83-9747-b537-35e578b719ef', 'request_id': 'a86d32bb-dd83-9747-b537-35e578b719ef'}
2025-08-05 23:08:18,481 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:18,482 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-565cd7de-e5c2-957f-8ad2-62b9fc314e4e', 'request_id': '565cd7de-e5c2-957f-8ad2-62b9fc314e4e'}
2025-08-05 23:08:22,640 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:22,641 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-f1a1142e-974b-9705-aacd-5ef13c513b12', 'request_id': 'f1a1142e-974b-9705-aacd-5ef13c513b12'}
2025-08-05 23:08:22,642 - ERROR - 第 16 批处理失败: RetryError[<Future at 0x119e2ae60 state=finished raised BadRequestError>]
2025-08-05 23:08:22,642 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:08:22,642 - INFO - 开始处理第 17/21 批，包含 10 个单词
2025-08-05 23:08:22,719 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:22,720 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-2b1e9ae2-2b8c-95a3-94cb-4b24a356e0f8', 'request_id': '2b1e9ae2-2b8c-95a3-94cb-4b24a356e0f8'}
2025-08-05 23:08:26,792 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:26,793 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-d92dbcc9-b0b1-984b-bd0d-9ad53a149490', 'request_id': 'd92dbcc9-b0b1-984b-bd0d-9ad53a149490'}
2025-08-05 23:08:30,961 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:30,962 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-08b3ca7d-7cc7-9950-88e3-ad8696ea50e0', 'request_id': '08b3ca7d-7cc7-9950-88e3-ad8696ea50e0'}
2025-08-05 23:08:30,963 - ERROR - 第 17 批处理失败: RetryError[<Future at 0x119e4e290 state=finished raised BadRequestError>]
2025-08-05 23:08:30,963 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:08:30,963 - INFO - 开始处理第 18/21 批，包含 10 个单词
2025-08-05 23:08:31,123 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:31,125 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-4fd51cf9-6be5-92f8-a3a3-043c61dd2d4a', 'request_id': '4fd51cf9-6be5-92f8-a3a3-043c61dd2d4a'}
2025-08-05 23:08:35,267 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:35,268 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-ce531ef1-20ed-9609-b28e-43547393c663', 'request_id': 'ce531ef1-20ed-9609-b28e-43547393c663'}
2025-08-05 23:08:39,351 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:39,352 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-0db717da-3e07-951c-9ca3-6bb43218b44b', 'request_id': '0db717da-3e07-951c-9ca3-6bb43218b44b'}
2025-08-05 23:08:39,353 - ERROR - 第 18 批处理失败: RetryError[<Future at 0x1198b83a0 state=finished raised BadRequestError>]
2025-08-05 23:08:39,353 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:08:39,353 - INFO - 开始处理第 19/21 批，包含 10 个单词
2025-08-05 23:08:39,547 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:39,549 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-ff48256a-22c7-9959-993b-36b8628e4088', 'request_id': 'ff48256a-22c7-9959-993b-36b8628e4088'}
2025-08-05 23:08:43,672 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:43,673 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-087b6223-e6d7-9334-bef8-21c4aae7043f', 'request_id': '087b6223-e6d7-9334-bef8-21c4aae7043f'}
2025-08-05 23:08:47,763 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:47,764 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-f7782a91-6f47-91b9-a1c9-f6b6d1420c5c', 'request_id': 'f7782a91-6f47-91b9-a1c9-f6b6d1420c5c'}
2025-08-05 23:08:47,764 - ERROR - 第 19 批处理失败: RetryError[<Future at 0x119ec68c0 state=finished raised BadRequestError>]
2025-08-05 23:08:47,764 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:08:47,764 - INFO - 开始处理第 20/21 批，包含 10 个单词
2025-08-05 23:08:47,909 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:47,910 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-402fe889-2729-9ff3-b2b1-d46f65970113', 'request_id': '402fe889-2729-9ff3-b2b1-d46f65970113'}
2025-08-05 23:08:52,000 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:52,001 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-860d345a-7178-9336-8b55-942d17164725', 'request_id': '860d345a-7178-9336-8b55-942d17164725'}
2025-08-05 23:08:56,163 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:56,165 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-5d06fc73-3c2a-968e-917d-d556cf6378a3', 'request_id': '5d06fc73-3c2a-968e-917d-d556cf6378a3'}
2025-08-05 23:08:56,165 - ERROR - 第 20 批处理失败: RetryError[<Future at 0x119e6c5b0 state=finished raised BadRequestError>]
2025-08-05 23:08:56,165 - WARNING - 使用备用例句格式，共 10 个
2025-08-05 23:08:56,165 - INFO - 开始处理第 21/21 批，包含 5 个单词
2025-08-05 23:08:56,320 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:08:56,321 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-b8a50e26-0879-9752-8132-8552add7fbda', 'request_id': 'b8a50e26-0879-9752-8132-8552add7fbda'}
2025-08-05 23:09:00,480 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:09:00,482 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-abd8fbbd-ffa2-9fbc-94aa-dd7a71e5f272', 'request_id': 'abd8fbbd-ffa2-9fbc-94aa-dd7a71e5f272'}
2025-08-05 23:09:04,578 - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-05 23:09:04,580 - ERROR - API调用失败: Error code: 400 - {'error': {'code': 'Arrearage', 'param': None, 'message': 'Access denied, please make sure your account is in good standing.', 'type': 'Arrearage'}, 'id': 'chatcmpl-1c33be2c-2cde-98f0-887b-886404392af2', 'request_id': '1c33be2c-2cde-98f0-887b-886404392af2'}
2025-08-05 23:09:04,580 - ERROR - 第 21 批处理失败: RetryError[<Future at 0x119e2ba30 state=finished raised BadRequestError>]
2025-08-05 23:09:04,580 - WARNING - 使用备用例句格式，共 5 个
2025-08-05 23:09:04,580 - INFO - 所有批次处理完成，共生成 205 个例句
2025-08-05 23:09:04,590 - INFO - 结果已保存到: 小猪佩奇_sentences_20250805_230904.json
2025-08-05 23:09:04,592 - INFO - 生成报告已保存: 小猪佩奇_sentences_20250805_230904_report.md
2025-08-05 23:23:01,823 - INFO - 成功加载小学词汇表，共 531 个单词
2025-08-05 23:23:01,824 - INFO - 将 205 个单词分为 41 批，每批最多 5 个
2025-08-05 23:23:01,824 - INFO - 开始处理第 1/41 批，包含 5 个单词
2025-08-05 23:23:02,257 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 0217544073823381c7a4e8a753969d21d36e5054d31f316819235","param":"","type":"NotFound"}}
2025-08-05 23:23:06,491 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 0217544073865735667e58f5653ea62900d719dbb212d3dd53d19","param":"","type":"NotFound"}}
2025-08-05 23:23:10,752 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 021754407390812eb49daf9acf049b217c8fde4b868ba5b2b91b0","param":"","type":"NotFound"}}
2025-08-05 23:23:10,752 - ERROR - 第 1 批处理失败: RetryError[<Future at 0x106554730 state=finished raised Exception>]
2025-08-05 23:23:10,753 - WARNING - 使用备用例句格式，共 5 个
2025-08-05 23:23:10,753 - INFO - 开始处理第 2/41 批，包含 5 个单词
2025-08-05 23:23:10,973 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 021754407391054285b6dcb155b60affdba1935ab3747e2895495","param":"","type":"NotFound"}}
2025-08-05 23:23:15,214 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 02175440739529264080b2f557e90bf8e4f1e2a44cf6c29b634d2","param":"","type":"NotFound"}}
2025-08-05 23:23:19,454 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 0217544073995353e7204cd77e127966994cff466b8693e5a4acc","param":"","type":"NotFound"}}
2025-08-05 23:23:19,455 - ERROR - 第 2 批处理失败: RetryError[<Future at 0x106556d70 state=finished raised Exception>]
2025-08-05 23:23:19,455 - WARNING - 使用备用例句格式，共 5 个
2025-08-05 23:23:19,456 - INFO - 开始处理第 3/41 批，包含 5 个单词
2025-08-05 23:23:19,697 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 0217544073997747a20ba5082a436aed6033d266df24c7e80f178","param":"","type":"NotFound"}}
2025-08-05 23:23:24,256 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 021754407404337ec963d5993fde85ff706a8094e8755bd44a76e","param":"","type":"NotFound"}}
2025-08-05 23:23:28,497 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 02175440740857312cea654c967d03eb709107f3e8697a5b7dd2f","param":"","type":"NotFound"}}
2025-08-05 23:23:28,498 - ERROR - 第 3 批处理失败: RetryError[<Future at 0x106557610 state=finished raised Exception>]
2025-08-05 23:23:28,498 - WARNING - 使用备用例句格式，共 5 个
2025-08-05 23:23:28,498 - INFO - 开始处理第 4/41 批，包含 5 个单词
2025-08-05 23:23:28,736 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 0217544074088121d9a93d2a16e5dad9e4b1944713056634f1f0d","param":"","type":"NotFound"}}
2025-08-05 23:23:32,995 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 021754407413055f828f4e64d52343578b1beb6b0d50f738e8a30","param":"","type":"NotFound"}}
2025-08-05 23:23:37,214 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 021754407417293e2db2f94dd3369291ecdb9ac35cb6692431af7","param":"","type":"NotFound"}}
2025-08-05 23:23:37,215 - ERROR - 第 4 批处理失败: RetryError[<Future at 0x106557eb0 state=finished raised Exception>]
2025-08-05 23:23:37,215 - WARNING - 使用备用例句格式，共 5 个
2025-08-05 23:23:37,215 - INFO - 开始处理第 5/41 批，包含 5 个单词
2025-08-05 23:23:37,467 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 021754407417543708dd9b1dbdfde3f91c67e3002f7c15fadfa7e","param":"","type":"NotFound"}}
2025-08-05 23:23:41,792 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 0217544074218539ad1cb7a7f4b31142b80a02e993b5a8f7180ec","param":"","type":"NotFound"}}
2025-08-05 23:23:46,043 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 0217544074260959ad1cb7a7f4b31142b80a02e993b5a8f91bd5e","param":"","type":"NotFound"}}
2025-08-05 23:23:46,044 - ERROR - 第 5 批处理失败: RetryError[<Future at 0x106557c70 state=finished raised Exception>]
2025-08-05 23:23:46,044 - WARNING - 使用备用例句格式，共 5 个
2025-08-05 23:23:46,044 - INFO - 开始处理第 6/41 批，包含 5 个单词
2025-08-05 23:23:46,264 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 021754407426335e3a294774db46d0edab2e7a3c8146849f5a267","param":"","type":"NotFound"}}
2025-08-05 23:23:50,510 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 021754407430585d24e408a3ff217b55cf08c9e1c654363eb5726","param":"","type":"NotFound"}}
2025-08-05 23:23:54,820 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 021754407434896747eea32fc41a3946f1fed981da9801dd31b6b","param":"","type":"NotFound"}}
2025-08-05 23:23:54,820 - ERROR - 第 6 批处理失败: RetryError[<Future at 0x1065575e0 state=finished raised Exception>]
2025-08-05 23:23:54,821 - WARNING - 使用备用例句格式，共 5 个
2025-08-05 23:23:54,821 - INFO - 开始处理第 7/41 批，包含 5 个单词
2025-08-05 23:23:55,062 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 0217544074351339ad1cb7a7f4b31142b80a02e993b5a8fa3abbe","param":"","type":"NotFound"}}
2025-08-05 23:23:59,321 - ERROR - API调用失败: API调用失败: HTTP 404, {"error":{"code":"InvalidEndpointOrModel.NotFound","message":"The model or endpoint doubao-pro-4k does not exist or you do not have access to it. Request id: 021754407439373285b6dcb155b60affdba1935ab3747e2c510ea","param":"","type":"NotFound"}}
2025-08-05 23:36:33,413 - INFO - 成功加载小学词汇表，共 531 个单词
2025-08-05 23:36:33,414 - INFO - 将 205 个单词分为 41 批，每批最多 5 个
2025-08-05 23:36:33,414 - INFO - 开始处理第 1/41 批，包含 5 个单词
2025-08-05 23:36:46,000 - INFO - API响应成功，处理了 5 个单词
2025-08-05 23:36:46,002 - INFO - 成功解析 5 个例句
2025-08-05 23:36:46,003 - INFO - 第 1 批处理完成，生成 5 个例句
2025-08-05 23:36:46,003 - INFO - 等待 2.0 秒后处理下一批...
2025-08-05 23:36:48,004 - INFO - 开始处理第 2/41 批，包含 5 个单词
2025-08-05 23:37:01,277 - INFO - API响应成功，处理了 5 个单词
2025-08-05 23:37:01,278 - INFO - 成功解析 5 个例句
2025-08-05 23:37:01,279 - INFO - 第 2 批处理完成，生成 5 个例句
2025-08-05 23:37:01,279 - INFO - 等待 2.0 秒后处理下一批...
2025-08-05 23:37:03,281 - INFO - 开始处理第 3/41 批，包含 5 个单词
2025-08-05 23:37:15,524 - INFO - API响应成功，处理了 5 个单词
2025-08-05 23:37:15,525 - INFO - 成功解析 5 个例句
2025-08-05 23:37:15,526 - INFO - 第 3 批处理完成，生成 5 个例句
2025-08-05 23:37:15,526 - INFO - 等待 2.0 秒后处理下一批...
2025-08-05 23:37:17,528 - INFO - 开始处理第 4/41 批，包含 5 个单词
2025-08-05 23:37:32,196 - INFO - API响应成功，处理了 5 个单词
2025-08-05 23:37:32,197 - INFO - 成功解析 5 个例句
2025-08-05 23:37:32,198 - INFO - 第 4 批处理完成，生成 5 个例句
2025-08-05 23:37:32,198 - INFO - 等待 2.0 秒后处理下一批...
2025-08-05 23:37:34,199 - INFO - 开始处理第 5/41 批，包含 5 个单词
2025-08-05 23:37:48,036 - INFO - API响应成功，处理了 5 个单词
2025-08-05 23:37:48,037 - INFO - 成功解析 5 个例句
2025-08-05 23:37:48,038 - INFO - 第 5 批处理完成，生成 5 个例句
2025-08-05 23:37:48,038 - INFO - 等待 2.0 秒后处理下一批...
2025-08-05 23:37:50,040 - INFO - 开始处理第 6/41 批，包含 5 个单词
2025-08-05 23:38:02,108 - INFO - API响应成功，处理了 5 个单词
2025-08-05 23:38:02,108 - INFO - 成功解析 5 个例句
2025-08-05 23:38:02,109 - INFO - 第 6 批处理完成，生成 5 个例句
2025-08-05 23:38:02,109 - INFO - 等待 2.0 秒后处理下一批...
2025-08-05 23:38:04,110 - INFO - 开始处理第 7/41 批，包含 5 个单词
