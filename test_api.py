#!/usr/bin/env python3
"""
测试豆包API配置
"""

import json
import requests

def test_api():
    """测试API配置"""
    
    # API配置
    api_key = "596126b8-9fe0-4f89-9ad2-e4389accc218"
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 尝试不同的模型名称
    models_to_test = [
        "doubao-pro-4k",
        "doubao-lite-4k", 
        "doubao-pro-32k",
        "ep-20241205140956-8xqzx",  # 可能的端点ID
        "Doubao-pro-4k",
        "DOUBAO-PRO-4K"
    ]
    
    for model in models_to_test:
        print(f"\n🧪 测试模型: {model}")
        
        data = {
            "model": model,
            "messages": [
                {
                    "role": "user", 
                    "content": "你好，请回复'测试成功'"
                }
            ],
            "max_tokens": 100,
            "temperature": 0.7
        }
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if "choices" in result and result["choices"]:
                    ai_response = result["choices"][0]["message"]["content"]
                    print(f"✅ 成功! 回复: {ai_response}")
                    print(f"✅ 可用模型: {model}")
                    return model  # 返回第一个可用的模型
                else:
                    print(f"❌ 响应格式异常: {result}")
            else:
                error_info = response.text
                print(f"❌ 失败: {error_info}")
                
        except Exception as e:
            print(f"❌ 异常: {str(e)}")
    
    print("\n❌ 所有模型都测试失败")
    return None

def test_different_endpoints():
    """测试不同的端点"""
    
    api_key = "596126b8-9fe0-4f89-9ad2-e4389accc218"
    
    endpoints_to_test = [
        "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
        "https://ark.cn-beijing.volces.com/api/v3/completions",
        "https://open.volcengineapi.com/api/v3/chat/completions"
    ]
    
    for endpoint in endpoints_to_test:
        print(f"\n🌐 测试端点: {endpoint}")
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        data = {
            "model": "doubao-pro-4k",
            "messages": [
                {
                    "role": "user", 
                    "content": "测试"
                }
            ],
            "max_tokens": 50
        }
        
        try:
            response = requests.post(endpoint, headers=headers, json=data, timeout=30)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"✅ 端点可用: {endpoint}")
                return endpoint
            else:
                print(f"❌ 端点失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 端点异常: {str(e)}")
    
    return None

if __name__ == "__main__":
    print("🔍 豆包API配置测试")
    print("=" * 50)
    
    # 测试模型
    working_model = test_api()
    
    if not working_model:
        print("\n🔄 尝试不同端点...")
        working_endpoint = test_different_endpoints()
        
        if working_endpoint:
            print(f"\n✅ 找到可用端点: {working_endpoint}")
        else:
            print("\n❌ 所有测试都失败，请检查:")
            print("1. API密钥是否正确")
            print("2. 账户是否有权限")
            print("3. 网络连接是否正常")
            print("4. 模型名称是否正确")
    else:
        print(f"\n🎉 测试完成! 可用模型: {working_model}")
