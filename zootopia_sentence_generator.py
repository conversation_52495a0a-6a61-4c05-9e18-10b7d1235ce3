#!/usr/bin/env python3
"""
疯狂动物城IP主题小学英语例句生成器
基于阿里云百炼API，为小学生生成符合难度标准的英语例句
"""

import asyncio
import json
import logging
import os
import time
import argparse
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

from openai import OpenAI
from tenacity import retry, stop_after_attempt, wait_exponential

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('zootopia_generator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class GeneratorConfig:
    """生成器配置"""
    api_key: str = "sk-7c98626b87c04f1297864c9bdb5e0b44"
    batch_size: int = 25  # 每批处理的单词数量
    max_retries: int = 3
    delay_between_batches: float = 2.0  # 批次间延迟（秒）
    ip_theme: str = "zootopia"  # IP主题：zootopia 或 peppa_pig

class IPSentenceAPIClient:
    """IP主题例句生成API客户端"""

    def __init__(self, api_key: str, ip_theme: str = "zootopia"):
        self.client = OpenAI(
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        self.ip_theme = ip_theme

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def generate_sentences(self, words: List[str], difficulty_guide: str) -> List[Dict[str, Any]]:
        """为一批单词生成IP主题例句"""

        # 构建提示词
        prompt = self._build_prompt(words, difficulty_guide)
        
        try:
            completion = self.client.chat.completions.create(
                model="qwen-plus",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=4000
            )
            
            response_text = completion.choices[0].message.content
            logger.info(f"API响应成功，处理了 {len(words)} 个单词")
            
            # 解析响应
            return self._parse_response(response_text, words)
            
        except Exception as e:
            logger.error(f"API调用失败: {e}")
            raise

    def _build_prompt(self, words: List[str], difficulty_guide: str) -> str:
        """构建生成提示词"""
        words_str = "、".join(words)

        if self.ip_theme == "peppa_pig":
            characters_info = """
**小猪佩奇角色参考** (主要以佩奇的视角):
- Peppa Pig (佩奇) - 小猪，活泼可爱的主角
- George Pig (乔治) - 佩奇的弟弟，喜欢恐龙
- Daddy Pig (猪爸爸) - 佩奇的爸爸，温和幽默
- Mummy Pig (猪妈妈) - 佩奇的妈妈，聪明能干
- Grandpa Pig (猪爷爷) - 佩奇的爷爷，喜欢园艺
- Grandma Pig (猪奶奶) - 佩奇的奶奶，慈祥温柔
- Suzy Sheep (苏西羊) - 佩奇的好朋友
- Pedro Pony (佩德罗小马) - 佩奇的同学
"""
            theme_name = "小猪佩奇"
            example1 = '"单词": "apple", "英文例句": "Peppa likes to eat red apples with George.", "中文翻译": "佩奇喜欢和乔治一起吃红苹果。"'
            example2 = '"单词": "run", "英文例句": "Peppa can run fast in the garden.", "中文翻译": "佩奇能在花园里跑得很快。"'
        else:  # zootopia
            characters_info = """
**疯狂动物城角色参考**:
- Judy Hopps (朱迪·霍普斯) - 兔子警官
- Nick Wilde (尼克·王尔德) - 狐狸
- Chief Bogo (博格局长) - 水牛警察局长
- Flash (闪电) - 树懒
- Gazelle (夏奇羊) - 羚羊歌星
- Mayor Lionheart (狮明德市长) - 狮子市长
- Bellwether (贝尔维瑟) - 绵羊副市长
"""
            theme_name = "疯狂动物城"
            example1 = '"单词": "apple", "英文例句": "Judy Hopps eats a red apple for lunch.", "中文翻译": "朱迪·霍普斯午餐吃了一个红苹果。"'
            example2 = '"单词": "run", "英文例句": "Nick can run very fast in the city.", "中文翻译": "尼克在城市里跑得很快。"'

        prompt = f"""
你是一位专业的小学英语教师，需要为以下单词创建{theme_name}主题的英语例句。

**单词列表**: {words_str}

{characters_info}

**小学英语难度要求**:
{difficulty_guide}

**生成要求**:
1. 每个单词生成一个例句，必须包含该单词
2. 例句必须融入{theme_name}的角色和场景
3. 严格遵循小学英语难度标准：
   - 1-2年级：4-6个词，主谓宾结构
   - 3-4年级：可用现在进行时、简单疑问句
   - 5-6年级：可用过去时、情态动词
4. 所有词汇不得超出小学范围
5. 提供准确的中文翻译

**输出格式** (严格按照JSON格式):
```json
[
  {{
    {example1}
  }},
  {{
    {example2}
  }}
]
```

请为所有单词生成例句，确保JSON格式正确。
"""
        return prompt

    def _parse_response(self, response_text: str, words: List[str]) -> List[Dict[str, Any]]:
        """解析API响应"""
        try:
            # 清理响应文本，提取JSON部分
            cleaned_response = self._clean_json_response(response_text)
            parsed_data = json.loads(cleaned_response)
            
            # 验证数据完整性
            if not isinstance(parsed_data, list):
                raise ValueError("响应不是列表格式")
            
            # 为每个结果添加序号
            for i, item in enumerate(parsed_data, 1):
                item["序号"] = i
            
            logger.info(f"成功解析 {len(parsed_data)} 个例句")
            return parsed_data
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            logger.debug(f"原始响应: {response_text}")
            # 返回备用格式
            return self._create_fallback_sentences(words)
        except Exception as e:
            logger.error(f"响应解析异常: {e}")
            return self._create_fallback_sentences(words)

    def _clean_json_response(self, response: str) -> str:
        """清理AI响应中的markdown格式，提取纯JSON"""
        response = response.strip()
        
        # 移除markdown代码块标记
        if '```json' in response:
            start = response.find('```json') + 7
            end = response.rfind('```')
            if end > start:
                response = response[start:end]
        elif '```' in response:
            start = response.find('```') + 3
            end = response.rfind('```')
            if end > start:
                response = response[start:end]
        
        # 查找JSON数组的开始和结束
        start_bracket = response.find('[')
        end_bracket = response.rfind(']')
        
        if start_bracket != -1 and end_bracket != -1 and end_bracket > start_bracket:
            response = response[start_bracket:end_bracket + 1]
        
        return response.strip()

    def _create_fallback_sentences(self, words: List[str]) -> List[Dict[str, Any]]:
        """创建备用例句（当API解析失败时）"""
        fallback_sentences = []
        
        for i, word in enumerate(words, 1):
            if self.ip_theme == "peppa_pig":
                sentence = {
                    "序号": i,
                    "单词": word,
                    "英文例句": f"Peppa uses the word '{word}' with her family.",
                    "中文翻译": f"佩奇和家人一起使用单词'{word}'。"
                }
            else:  # zootopia
                sentence = {
                    "序号": i,
                    "单词": word,
                    "英文例句": f"Judy and Nick use the word '{word}' in Zootopia.",
                    "中文翻译": f"朱迪和尼克在疯狂动物城使用单词'{word}'。"
                }
            fallback_sentences.append(sentence)
        
        logger.warning(f"使用备用例句格式，共 {len(fallback_sentences)} 个")
        return fallback_sentences


class IPSentenceGenerator:
    """IP主题例句生成器主类"""

    def __init__(self, config: GeneratorConfig):
        self.config = config
        self.api_client = IPSentenceAPIClient(config.api_key, config.ip_theme)
        self.difficulty_guide = ""

    def load_word_list(self, file_path: str) -> List[str]:
        """加载单词表"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                words = [line.strip() for line in f if line.strip()]

            logger.info(f"成功加载 {len(words)} 个单词")
            return words

        except Exception as e:
            logger.error(f"加载单词表失败: {e}")
            raise

    def load_difficulty_guide(self, file_path: str) -> str:
        """加载小学英语难度指南"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            logger.info("成功加载小学英语难度指南")
            return content

        except Exception as e:
            logger.error(f"加载难度指南失败: {e}")
            raise

    def split_into_batches(self, words: List[str]) -> List[List[str]]:
        """将单词列表分批处理"""
        batches = []
        for i in range(0, len(words), self.config.batch_size):
            batch = words[i:i + self.config.batch_size]
            batches.append(batch)

        logger.info(f"将 {len(words)} 个单词分为 {len(batches)} 批，每批最多 {self.config.batch_size} 个")
        return batches

    async def generate_all_sentences(self, words: List[str]) -> List[Dict[str, Any]]:
        """生成所有单词的例句"""
        all_sentences = []
        batches = self.split_into_batches(words)

        for batch_idx, batch in enumerate(batches, 1):
            logger.info(f"开始处理第 {batch_idx}/{len(batches)} 批，包含 {len(batch)} 个单词")

            try:
                # 生成当前批次的例句
                batch_sentences = await self.api_client.generate_sentences(
                    batch, self.difficulty_guide
                )

                # 更新序号（全局连续）
                start_index = len(all_sentences) + 1
                for i, sentence in enumerate(batch_sentences):
                    sentence["序号"] = start_index + i

                all_sentences.extend(batch_sentences)

                logger.info(f"第 {batch_idx} 批处理完成，生成 {len(batch_sentences)} 个例句")

                # 批次间延迟
                if batch_idx < len(batches):
                    logger.info(f"等待 {self.config.delay_between_batches} 秒后处理下一批...")
                    await asyncio.sleep(self.config.delay_between_batches)

            except Exception as e:
                logger.error(f"第 {batch_idx} 批处理失败: {e}")
                # 创建备用例句
                fallback_sentences = self.api_client._create_fallback_sentences(batch)
                start_index = len(all_sentences) + 1
                for i, sentence in enumerate(fallback_sentences):
                    sentence["序号"] = start_index + i
                all_sentences.extend(fallback_sentences)

        logger.info(f"所有批次处理完成，共生成 {len(all_sentences)} 个例句")
        return all_sentences

    def save_results(self, sentences: List[Dict[str, Any]], output_file: str):
        """保存生成结果"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(sentences, f, ensure_ascii=False, indent=2)

            logger.info(f"结果已保存到: {output_file}")

        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            raise

    def print_progress_summary(self, sentences: List[Dict[str, Any]], output_file: str):
        """打印进度摘要"""
        theme_name = "小猪佩奇" if self.config.ip_theme == "peppa_pig" else "疯狂动物城"

        print("\n" + "="*60)
        print(f"🎉 {theme_name}例句生成完成！")
        print("="*60)
        print(f"📊 总计生成例句: {len(sentences)} 个")
        print(f"📁 输出文件: {output_file}")

        # 显示前几个例句作为预览
        print("\n📝 例句预览:")
        for i, sentence in enumerate(sentences[:5], 1):  # 显示前5个
            print(f"{i}. {sentence['单词']}: {sentence['英文例句']}")
            print(f"   翻译: {sentence['中文翻译']}")

        if len(sentences) > 5:
            print(f"   ... 还有 {len(sentences) - 5} 个例句")

        print("\n✅ 生成完成！请查看输出文件获取完整结果。")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="IP主题小学英语例句生成器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 生成疯狂动物城主题例句
  python3 zootopia_sentence_generator.py --theme zootopia

  # 生成小猪佩奇主题例句
  python3 zootopia_sentence_generator.py --theme peppa_pig

  # 自定义批次大小和延迟
  python3 zootopia_sentence_generator.py --theme peppa_pig --batch-size 20 --delay 3
        """
    )

    parser.add_argument(
        "--theme",
        choices=["zootopia", "peppa_pig"],
        default="zootopia",
        help="选择IP主题：zootopia(疯狂动物城) 或 peppa_pig(小猪佩奇)"
    )

    parser.add_argument(
        "--word-list",
        default="/Users/<USER>/Documents/wordAI/临时项目数据/word_list.txt",
        help="单词表文件路径"
    )

    parser.add_argument(
        "--difficulty-guide",
        default="/Users/<USER>/Documents/wordAI/临时项目数据/小学阶段英语语法和句子难度的系统汇总.txt",
        help="难度指南文件路径"
    )

    parser.add_argument(
        "--batch-size",
        type=int,
        default=25,
        help="每批处理的单词数量 (默认: 25)"
    )

    parser.add_argument(
        "--delay",
        type=float,
        default=2.0,
        help="批次间延迟时间(秒) (默认: 2.0)"
    )

    parser.add_argument(
        "--api-key",
        default="sk-7c98626b87c04f1297864c9bdb5e0b44",
        help="阿里云百炼API密钥"
    )

    return parser.parse_args()


async def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    theme_name = "小猪佩奇" if args.theme == "peppa_pig" else "疯狂动物城"
    theme_emoji = "🐷" if args.theme == "peppa_pig" else "🦊"

    print(f"{theme_emoji} {theme_name}IP主题小学英语例句生成器")
    print("="*60)
    print(f"🎯 主题: {theme_name}")
    print(f"📦 批次大小: {args.batch_size}")
    print(f"⏱️  批次延迟: {args.delay}秒")
    print("="*60)

    # 检查文件是否存在
    if not os.path.exists(args.word_list):
        logger.error(f"单词表文件不存在: {args.word_list}")
        return

    if not os.path.exists(args.difficulty_guide):
        logger.error(f"难度指南文件不存在: {args.difficulty_guide}")
        return

    try:
        # 初始化生成器
        config = GeneratorConfig(
            api_key=args.api_key,
            batch_size=args.batch_size,
            delay_between_batches=args.delay,
            ip_theme=args.theme
        )
        generator = IPSentenceGenerator(config)

        # 加载数据
        print("📚 加载单词表和难度指南...")
        words = generator.load_word_list(args.word_list)
        generator.difficulty_guide = generator.load_difficulty_guide(args.difficulty_guide)

        print(f"✅ 成功加载 {len(words)} 个单词")
        print(f"📋 将分 {len(generator.split_into_batches(words))} 批处理")

        # 开始生成
        print(f"\n🚀 开始生成{theme_name}主题例句...")
        start_time = time.time()

        sentences = await generator.generate_all_sentences(words)

        # 保存结果
        theme_prefix = "peppa_pig" if args.theme == "peppa_pig" else "zootopia"
        output_file = f"{theme_prefix}_sentences_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        generator.save_results(sentences, output_file)

        # 显示完成信息
        end_time = time.time()
        duration = end_time - start_time

        generator.print_progress_summary(sentences, output_file)
        print(f"⏱️  总耗时: {duration:.1f} 秒")

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"\n❌ 生成失败: {e}")
        print("请检查日志文件 zootopia_generator.log 获取详细错误信息")


if __name__ == "__main__":
    asyncio.run(main())
