#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包文本模型API - 简化版Demo
最简单的调用示例
"""

import json
import requests

def generate_text_simple():
    """最简单的文本生成示例"""
    
    # ========== 配置API密钥 ==========
    # 从你的项目中获取的密钥
    api_key = "596126b8-9fe0-4f89-9ad2-e4389accc218"
    
    # ========== API配置 ==========
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # ========== 设置生成参数 ==========
    data = {
        "model": "doubao-pro-4k",  # 模型名称
        "messages": [
            {
                "role": "system", 
                "content": "你是一个友善的AI助手，请用中文回答问题。"
            },
            {
                "role": "user", 
                "content": "请介绍一下人工智能的基本概念"
            }
        ],
        "max_tokens": 2000,      # 最大生成token数
        "temperature": 0.7,      # 温度参数 (0.0-1.0)
        "top_p": 0.9,           # 核采样参数
        "stream": False         # 是否流式返回
    }
    
    print("💬 正在生成文本...")
    print(f"用户问题: {data['messages'][1]['content']}")
    
    # ========== 调用API ==========
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        # ========== 处理响应 ==========
        if response.status_code == 200:
            result = response.json()
            
            if "choices" in result and result["choices"]:
                # 获取AI回复
                ai_response = result["choices"][0]["message"]["content"]
                print("✅ 生成成功!")
                print(f"AI回复: {ai_response}")
                
                # 显示token使用情况
                if "usage" in result:
                    usage = result["usage"]
                    print(f"\n📊 Token使用情况:")
                    print(f"  输入tokens: {usage.get('prompt_tokens', 0)}")
                    print(f"  输出tokens: {usage.get('completion_tokens', 0)}")
                    print(f"  总计tokens: {usage.get('total_tokens', 0)}")
                    
            else:
                print("❌ 响应中没有找到生成内容")
                print(f"完整响应: {result}")
                
        else:
            print(f"❌ API调用失败: HTTP {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")


def chat_with_history():
    """带对话历史的聊天示例"""
    
    print("\n🔄 多轮对话示例")
    print("=" * 30)
    
    # API配置
    api_key = "596126b8-9fe0-4f89-9ad2-e4389accc218"
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 对话历史
    conversation = [
        {"role": "system", "content": "你是一个专业的编程助手"},
        {"role": "user", "content": "什么是Python？"},
        {"role": "assistant", "content": "Python是一种高级编程语言，以其简洁易读的语法而闻名。它广泛用于Web开发、数据科学、人工智能等领域。"},
        {"role": "user", "content": "Python有什么优点？"}
    ]
    
    data = {
        "model": "doubao-pro-4k",
        "messages": conversation,
        "max_tokens": 1000,
        "temperature": 0.7
    }
    
    print("对话历史:")
    for msg in conversation:
        role = "用户" if msg["role"] == "user" else "AI" if msg["role"] == "assistant" else "系统"
        print(f"{role}: {msg['content']}")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            if "choices" in result:
                ai_reply = result["choices"][0]["message"]["content"]
                print(f"AI: {ai_reply}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 错误: {str(e)}")


def creative_writing_example():
    """创意写作示例"""
    
    print("\n✍️ 创意写作示例")
    print("=" * 30)
    
    # API配置
    api_key = "596126b8-9fe0-4f89-9ad2-e4389accc218"
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 创意写作请求
    data = {
        "model": "doubao-pro-4k",
        "messages": [
            {
                "role": "system", 
                "content": "你是一个专业的小说作家，擅长创作优美的文学作品。请用生动的语言和丰富的想象力来回答用户的创作要求。"
            },
            {
                "role": "user", 
                "content": "请为我创作一个关于古代女子在月夜花园中的唯美场景描述，大约200字。"
            }
        ],
        "max_tokens": 500,
        "temperature": 0.8,  # 提高创意性
        "top_p": 0.9
    }
    
    print(f"创作要求: {data['messages'][1]['content']}")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            if "choices" in result:
                creative_text = result["choices"][0]["message"]["content"]
                print(f"\n创作结果:\n{creative_text}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 错误: {str(e)}")


if __name__ == "__main__":
    print("🚀 豆包文本模型API简单调用演示")
    print("=" * 50)
    
    # 基本文本生成
    generate_text_simple()
    
    # 多轮对话
    chat_with_history()
    
    # 创意写作
    creative_writing_example()
    
    print("\n🎉 演示完成！")
