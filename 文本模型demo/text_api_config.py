#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包文本模型API配置说明
包含所有可用的参数和配置选项
"""

# ========== API密钥配置 ==========
TEXT_API_CONFIG = {
    # 从你的项目.env文件中获取的配置
    "ARK_API_KEY": "596126b8-9fe0-4f89-9ad2-e4389accc218",
    "BASE_URL": "https://ark.cn-beijing.volces.com/api/v3",
    "CHAT_ENDPOINT": "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
}

# ========== 可用模型列表 ==========
AVAILABLE_MODELS = {
    "doubao-pro-4k": {
        "description": "豆包专业版 4K上下文",
        "max_tokens": 4096,
        "best_for": "日常对话、文本生成、代码编写"
    },
    "doubao-pro-32k": {
        "description": "豆包专业版 32K上下文", 
        "max_tokens": 32768,
        "best_for": "长文档处理、复杂推理、大量上下文"
    },
    "doubao-lite-4k": {
        "description": "豆包轻量版 4K上下文",
        "max_tokens": 4096,
        "best_for": "快速响应、简单任务、成本敏感场景"
    }
}

# ========== 请求参数配置 ==========
REQUEST_PARAMETERS = {
    # 必需参数
    "model": "doubao-pro-4k",  # 模型名称
    "messages": [              # 消息列表
        {"role": "system", "content": "你是一个AI助手"},
        {"role": "user", "content": "用户消息"}
    ],
    
    # 可选参数
    "max_tokens": 2000,        # 最大生成token数 (1-4096)
    "temperature": 0.7,        # 温度参数 (0.0-1.0)
    "top_p": 0.9,             # 核采样参数 (0.0-1.0)
    "stream": False,          # 是否流式返回
    "stop": None,             # 停止词列表
    "presence_penalty": 0.0,   # 存在惩罚 (-2.0-2.0)
    "frequency_penalty": 0.0,  # 频率惩罚 (-2.0-2.0)
}

# ========== 消息角色说明 ==========
MESSAGE_ROLES = {
    "system": {
        "description": "系统消息，用于设定AI的行为和角色",
        "example": "你是一个专业的编程助手，请用简洁明了的语言回答问题。",
        "best_practices": [
            "在对话开始时设置",
            "明确定义AI的角色和行为",
            "提供具体的回答风格指导"
        ]
    },
    "user": {
        "description": "用户消息，表示用户的输入",
        "example": "请解释一下什么是机器学习？",
        "best_practices": [
            "清晰表达需求",
            "提供足够的上下文",
            "避免歧义表达"
        ]
    },
    "assistant": {
        "description": "AI助手的回复消息",
        "example": "机器学习是人工智能的一个分支...",
        "best_practices": [
            "用于多轮对话中的历史回复",
            "保持对话的连贯性",
            "提供准确的上下文"
        ]
    }
}

# ========== 参数详细说明 ==========
PARAMETER_DETAILS = {
    "temperature": {
        "range": "0.0 - 1.0",
        "default": 0.7,
        "description": "控制输出的随机性",
        "effects": {
            "0.0-0.3": "更确定性，适合事实性回答",
            "0.4-0.7": "平衡创意和准确性，适合大多数场景",
            "0.8-1.0": "更有创意，适合创作和头脑风暴"
        }
    },
    "top_p": {
        "range": "0.0 - 1.0", 
        "default": 0.9,
        "description": "核采样参数，控制词汇选择范围",
        "effects": {
            "0.1-0.5": "更保守的词汇选择",
            "0.6-0.9": "平衡的词汇多样性",
            "0.9-1.0": "更多样的词汇选择"
        }
    },
    "max_tokens": {
        "range": "1 - 4096",
        "default": 2000,
        "description": "最大生成token数量",
        "note": "1个中文字符约等于1-2个token"
    },
    "presence_penalty": {
        "range": "-2.0 - 2.0",
        "default": 0.0,
        "description": "存在惩罚，减少重复话题",
        "effects": {
            "负值": "鼓励重复相同话题",
            "0": "不施加惩罚",
            "正值": "减少重复话题，鼓励新话题"
        }
    },
    "frequency_penalty": {
        "range": "-2.0 - 2.0",
        "default": 0.0,
        "description": "频率惩罚，减少重复词汇",
        "effects": {
            "负值": "鼓励重复使用相同词汇",
            "0": "不施加惩罚", 
            "正值": "减少重复词汇，增加词汇多样性"
        }
    }
}

# ========== 预设配置模板 ==========
PRESET_CONFIGS = {
    "creative_writing": {
        "description": "创意写作配置",
        "params": {
            "temperature": 0.8,
            "top_p": 0.9,
            "max_tokens": 2000,
            "presence_penalty": 0.1,
            "frequency_penalty": 0.1
        }
    },
    "factual_qa": {
        "description": "事实性问答配置",
        "params": {
            "temperature": 0.3,
            "top_p": 0.7,
            "max_tokens": 1000,
            "presence_penalty": 0.0,
            "frequency_penalty": 0.0
        }
    },
    "code_generation": {
        "description": "代码生成配置",
        "params": {
            "temperature": 0.2,
            "top_p": 0.8,
            "max_tokens": 1500,
            "presence_penalty": 0.0,
            "frequency_penalty": 0.0
        }
    },
    "brainstorming": {
        "description": "头脑风暴配置",
        "params": {
            "temperature": 0.9,
            "top_p": 0.95,
            "max_tokens": 1500,
            "presence_penalty": 0.2,
            "frequency_penalty": 0.2
        }
    },
    "translation": {
        "description": "翻译任务配置",
        "params": {
            "temperature": 0.1,
            "top_p": 0.6,
            "max_tokens": 2000,
            "presence_penalty": 0.0,
            "frequency_penalty": 0.0
        }
    }
}

# ========== 系统提示词模板 ==========
SYSTEM_PROMPT_TEMPLATES = {
    "general_assistant": "你是一个友善、有帮助的AI助手。请用清晰、准确的中文回答用户的问题。",
    
    "creative_writer": "你是一个专业的创意写作助手，擅长创作各种类型的文学作品。请用生动的语言和丰富的想象力来回答用户的创作要求。",
    
    "code_expert": "你是一个专业的编程专家，精通多种编程语言。请提供准确、高效的代码解决方案，并包含必要的解释。",
    
    "translator": "你是一个专业的翻译专家，能够准确地在中文和其他语言之间进行翻译。请保持原文的语调和含义。",
    
    "teacher": "你是一个耐心的老师，善于用简单易懂的方式解释复杂的概念。请用循序渐进的方法来回答学生的问题。",
    
    "analyst": "你是一个专业的分析师，善于从多个角度分析问题。请提供客观、全面的分析和建议。"
}

# ========== 使用示例 ==========
def get_request_example(preset_name="factual_qa"):
    """获取请求示例"""
    
    preset = PRESET_CONFIGS.get(preset_name, PRESET_CONFIGS["factual_qa"])
    
    request_data = {
        "model": "doubao-pro-4k",
        "messages": [
            {
                "role": "system",
                "content": SYSTEM_PROMPT_TEMPLATES["general_assistant"]
            },
            {
                "role": "user", 
                "content": "请介绍一下人工智能的发展历史"
            }
        ],
        **preset["params"]
    }
    
    return request_data

# ========== 错误码说明 ==========
ERROR_CODES = {
    400: "请求参数错误",
    401: "API密钥无效或缺失",
    403: "权限不足或配额不足",
    429: "请求频率过高",
    500: "服务器内部错误",
    503: "服务暂时不可用"
}

# ========== 最佳实践建议 ==========
BEST_PRACTICES = {
    "prompt_engineering": [
        "使用清晰、具体的指令",
        "提供足够的上下文信息",
        "使用示例来说明期望的输出格式",
        "将复杂任务分解为简单步骤"
    ],
    "parameter_tuning": [
        "根据任务类型调整temperature",
        "创意任务使用较高temperature",
        "事实性任务使用较低temperature",
        "合理设置max_tokens避免截断"
    ],
    "conversation_management": [
        "保持对话历史的连贯性",
        "适时清理过长的对话历史",
        "使用system消息设定角色",
        "避免在单次请求中包含过多信息"
    ],
    "error_handling": [
        "实现重试机制",
        "处理网络超时",
        "检查API响应状态",
        "记录错误日志便于调试"
    ]
}

if __name__ == "__main__":
    # 打印配置信息
    print("📋 豆包文本模型API配置说明")
    print("=" * 50)
    
    print("\n🤖 可用模型:")
    for model, info in AVAILABLE_MODELS.items():
        print(f"  {model}: {info['description']}")
    
    print("\n🔧 关键参数:")
    for param, details in PARAMETER_DETAILS.items():
        print(f"  {param}: {details['description']}")
        print(f"    范围: {details['range']}, 默认: {details['default']}")
    
    print("\n📝 预设配置:")
    for preset, info in PRESET_CONFIGS.items():
        print(f"  {preset}: {info['description']}")
    
    print("\n💡 最佳实践:")
    for category, practices in BEST_PRACTICES.items():
        print(f"  {category}:")
        for practice in practices:
            print(f"    - {practice}")
