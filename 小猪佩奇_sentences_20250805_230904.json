{"metadata": {"ip_name": "小猪佩奇", "generation_time": "2025-08-05 23:09:04", "total_words": 205, "total_sentences": 205, "generation_method": "AI_API", "api_model": "qwen-plus"}, "words": {"a": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes a.", "sentence_zh": "佩奇喜欢a。", "word": "a", "word_meaning": "喜欢a", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'a'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "afternoon": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes afternoon.", "sentence_zh": "佩奇喜欢afternoon。", "word": "afternoon", "word_meaning": "喜欢afternoon", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'afternoon'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "age": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes age.", "sentence_zh": "佩奇喜欢age。", "word": "age", "word_meaning": "喜欢age", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'age'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "angry": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes angry.", "sentence_zh": "佩奇喜欢angry。", "word": "angry", "word_meaning": "喜欢angry", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'angry'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "animal": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes animal.", "sentence_zh": "佩奇喜欢animal。", "word": "animal", "word_meaning": "喜欢animal", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'animal'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "answer": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes answer.", "sentence_zh": "佩奇喜欢answer。", "word": "answer", "word_meaning": "喜欢answer", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'answer'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "apple": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes apple.", "sentence_zh": "佩奇喜欢apple。", "word": "apple", "word_meaning": "喜欢apple", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'apple'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "arm": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes arm.", "sentence_zh": "佩奇喜欢arm。", "word": "arm", "word_meaning": "喜欢arm", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'arm'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "art": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes art.", "sentence_zh": "佩奇喜欢art。", "word": "art", "word_meaning": "喜欢art", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'art'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "ask": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes ask.", "sentence_zh": "佩奇喜欢ask。", "word": "ask", "word_meaning": "喜欢ask", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'ask'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "astronaut": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes astronaut.", "sentence_zh": "佩奇喜欢astronaut。", "word": "astronaut", "word_meaning": "喜欢astronaut", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'astronaut'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "aunt": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes aunt.", "sentence_zh": "佩奇喜欢aunt。", "word": "aunt", "word_meaning": "喜欢aunt", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'aunt'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "autumn": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes autumn.", "sentence_zh": "佩奇喜欢autumn。", "word": "autumn", "word_meaning": "喜欢autumn", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'autumn'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "basketball": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes basketball.", "sentence_zh": "佩奇喜欢basketball。", "word": "basketball", "word_meaning": "喜欢basketball", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'basketball'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "bear": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes bear.", "sentence_zh": "佩奇喜欢bear。", "word": "bear", "word_meaning": "喜欢bear", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'bear'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "bee": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes bee.", "sentence_zh": "佩奇喜欢bee。", "word": "bee", "word_meaning": "喜欢bee", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'bee'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "beside": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes beside.", "sentence_zh": "佩奇喜欢beside。", "word": "beside", "word_meaning": "喜欢beside", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'beside'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "black": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes black.", "sentence_zh": "佩奇喜欢black。", "word": "black", "word_meaning": "喜欢black", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'black'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "blackboard": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes blackboard.", "sentence_zh": "佩奇喜欢blackboard。", "word": "blackboard", "word_meaning": "喜欢blackboard", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'blackboard'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "box": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes box.", "sentence_zh": "佩奇喜欢box。", "word": "box", "word_meaning": "喜欢box", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'box'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "bread": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes bread.", "sentence_zh": "佩奇喜欢bread。", "word": "bread", "word_meaning": "喜欢bread", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'bread'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "breakfast": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes breakfast.", "sentence_zh": "佩奇喜欢breakfast。", "word": "breakfast", "word_meaning": "喜欢breakfast", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'breakfast'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "brown": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes brown.", "sentence_zh": "佩奇喜欢brown。", "word": "brown", "word_meaning": "喜欢brown", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'brown'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "bus": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes bus.", "sentence_zh": "佩奇喜欢bus。", "word": "bus", "word_meaning": "喜欢bus", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'bus'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "buy": {"grade": "primary", "sentences": [{"sentence": "P<PERSON>pa likes buy.", "sentence_zh": "佩奇喜欢buy。", "word": "buy", "word_meaning": "喜欢buy", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'buy'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "bye": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes bye.", "sentence_zh": "佩奇喜欢bye。", "word": "bye", "word_meaning": "喜欢bye", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'bye'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "candle": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes candle.", "sentence_zh": "佩奇喜欢candle。", "word": "candle", "word_meaning": "喜欢candle", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'candle'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "cap": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes cap.", "sentence_zh": "佩奇喜欢cap。", "word": "cap", "word_meaning": "喜欢cap", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'cap'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "card": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes card.", "sentence_zh": "佩奇喜欢card。", "word": "card", "word_meaning": "喜欢card", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'card'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "chicken": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes chicken.", "sentence_zh": "佩奇喜欢chicken。", "word": "chicken", "word_meaning": "喜欢chicken", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'chicken'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "child": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes child.", "sentence_zh": "佩奇喜欢child。", "word": "child", "word_meaning": "喜欢child", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'child'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "china": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes china.", "sentence_zh": "佩奇喜欢china。", "word": "china", "word_meaning": "喜欢china", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'china'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "chinese": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes chinese.", "sentence_zh": "佩奇喜欢chinese。", "word": "chinese", "word_meaning": "喜欢chinese", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'chinese'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "chore": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes chore.", "sentence_zh": "佩奇喜欢chore。", "word": "chore", "word_meaning": "喜欢chore", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'chore'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "cinema": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes cinema.", "sentence_zh": "佩奇喜欢cinema。", "word": "cinema", "word_meaning": "喜欢cinema", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'cinema'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "city": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes city.", "sentence_zh": "佩奇喜欢city。", "word": "city", "word_meaning": "喜欢city", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'city'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "class": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes class.", "sentence_zh": "佩奇喜欢class。", "word": "class", "word_meaning": "喜欢class", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'class'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "classroom": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes classroom.", "sentence_zh": "佩奇喜欢classroom。", "word": "classroom", "word_meaning": "喜欢classroom", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'classroom'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "clever": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes clever.", "sentence_zh": "佩奇喜欢clever。", "word": "clever", "word_meaning": "喜欢clever", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'clever'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "cloudy": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes cloudy.", "sentence_zh": "佩奇喜欢cloudy。", "word": "cloudy", "word_meaning": "喜欢cloudy", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'cloudy'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "coat": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes coat.", "sentence_zh": "佩奇喜欢coat。", "word": "coat", "word_meaning": "喜欢coat", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'coat'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "color": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes color.", "sentence_zh": "佩奇喜欢color。", "word": "color", "word_meaning": "喜欢color", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'color'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "colour": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes colour.", "sentence_zh": "佩奇喜欢colour。", "word": "colour", "word_meaning": "喜欢colour", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'colour'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "computer": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes computer.", "sentence_zh": "佩奇喜欢computer。", "word": "computer", "word_meaning": "喜欢computer", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'computer'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "cow": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes cow.", "sentence_zh": "佩奇喜欢cow。", "word": "cow", "word_meaning": "喜欢cow", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'cow'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "cry": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes cry.", "sentence_zh": "佩奇喜欢cry。", "word": "cry", "word_meaning": "喜欢cry", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'cry'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "cup": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes cup.", "sentence_zh": "佩奇喜欢cup。", "word": "cup", "word_meaning": "喜欢cup", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'cup'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "cut": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes cut.", "sentence_zh": "佩奇喜欢cut。", "word": "cut", "word_meaning": "喜欢cut", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'cut'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "cute": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes cute.", "sentence_zh": "佩奇喜欢cute。", "word": "cute", "word_meaning": "喜欢cute", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'cute'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "desk": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes desk.", "sentence_zh": "佩奇喜欢desk。", "word": "desk", "word_meaning": "喜欢desk", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'desk'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "dinner": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes dinner.", "sentence_zh": "佩奇喜欢dinner。", "word": "dinner", "word_meaning": "喜欢dinner", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'dinner'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "doctor": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes doctor.", "sentence_zh": "佩奇喜欢doctor。", "word": "doctor", "word_meaning": "喜欢doctor", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'doctor'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "draw": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes draw.", "sentence_zh": "佩奇喜欢draw。", "word": "draw", "word_meaning": "喜欢draw", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'draw'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "driver": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes driver.", "sentence_zh": "佩奇喜欢driver。", "word": "driver", "word_meaning": "喜欢driver", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'driver'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "duck": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes duck.", "sentence_zh": "佩奇喜欢duck。", "word": "duck", "word_meaning": "喜欢duck", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'duck'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "ear": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes ear.", "sentence_zh": "佩奇喜欢ear。", "word": "ear", "word_meaning": "喜欢ear", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'ear'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "elephant": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes elephant.", "sentence_zh": "佩奇喜欢elephant。", "word": "elephant", "word_meaning": "喜欢elephant", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'elephant'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "email": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes email.", "sentence_zh": "佩奇喜欢email。", "word": "email", "word_meaning": "喜欢email", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'email'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "english": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes english.", "sentence_zh": "佩奇喜欢english。", "word": "english", "word_meaning": "喜欢english", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'english'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "famous": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes famous.", "sentence_zh": "佩奇喜欢famous。", "word": "famous", "word_meaning": "喜欢famous", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'famous'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "fan": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes fan.", "sentence_zh": "佩奇喜欢fan。", "word": "fan", "word_meaning": "喜欢fan", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'fan'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "farm": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes farm.", "sentence_zh": "佩奇喜欢farm。", "word": "farm", "word_meaning": "喜欢farm", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'farm'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "farmer": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes farmer.", "sentence_zh": "佩奇喜欢farmer。", "word": "farmer", "word_meaning": "喜欢farmer", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'farmer'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "father": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes father.", "sentence_zh": "佩奇喜欢father。", "word": "father", "word_meaning": "喜欢father", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'father'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "favourite": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes favourite.", "sentence_zh": "佩奇喜欢favourite。", "word": "favourite", "word_meaning": "喜欢favourite", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'favourite'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "feel": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes feel.", "sentence_zh": "佩奇喜欢feel。", "word": "feel", "word_meaning": "喜欢feel", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'feel'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "feet": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes feet.", "sentence_zh": "佩奇喜欢feet。", "word": "feet", "word_meaning": "喜欢feet", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'feet'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "fish": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes fish.", "sentence_zh": "佩奇喜欢fish。", "word": "fish", "word_meaning": "喜欢fish", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'fish'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "flower": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes flower.", "sentence_zh": "佩奇喜欢flower。", "word": "flower", "word_meaning": "喜欢flower", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'flower'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "fly": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes fly.", "sentence_zh": "佩奇喜欢fly。", "word": "fly", "word_meaning": "喜欢fly", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'fly'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "foot": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes foot.", "sentence_zh": "佩奇喜欢foot。", "word": "foot", "word_meaning": "喜欢foot", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'foot'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "football": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes football.", "sentence_zh": "佩奇喜欢football。", "word": "football", "word_meaning": "喜欢football", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'football'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "free": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes free.", "sentence_zh": "佩奇喜欢free。", "word": "free", "word_meaning": "喜欢free", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'free'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "gift": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes gift.", "sentence_zh": "佩奇喜欢gift。", "word": "gift", "word_meaning": "喜欢gift", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'gift'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "glass": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes glass.", "sentence_zh": "佩奇喜欢glass。", "word": "glass", "word_meaning": "喜欢glass", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'glass'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "goodbye": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes goodbye.", "sentence_zh": "佩奇喜欢goodbye。", "word": "goodbye", "word_meaning": "喜欢goodbye", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'goodbye'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "grandfather": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes grandfather.", "sentence_zh": "佩奇喜欢grandfather。", "word": "grandfather", "word_meaning": "喜欢grandfather", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'grandfather'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "grandmother": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes grandmother.", "sentence_zh": "佩奇喜欢grandmother。", "word": "grandmother", "word_meaning": "喜欢grandmother", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'grandmother'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "grape": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes grape.", "sentence_zh": "佩奇喜欢grape。", "word": "grape", "word_meaning": "喜欢grape", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'grape'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "grass": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes grass.", "sentence_zh": "佩奇喜欢grass。", "word": "grass", "word_meaning": "喜欢grass", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'grass'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "green": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes green.", "sentence_zh": "佩奇喜欢green。", "word": "green", "word_meaning": "喜欢green", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'green'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "hair": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes hair.", "sentence_zh": "佩奇喜欢hair。", "word": "hair", "word_meaning": "喜欢hair", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'hair'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "half": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes half.", "sentence_zh": "佩奇喜欢half。", "word": "half", "word_meaning": "喜欢half", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'half'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "healthy": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes healthy.", "sentence_zh": "佩奇喜欢healthy。", "word": "healthy", "word_meaning": "喜欢healthy", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'healthy'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "heavy": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes heavy.", "sentence_zh": "佩奇喜欢heavy。", "word": "heavy", "word_meaning": "喜欢heavy", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'heavy'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "helpful": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes helpful.", "sentence_zh": "佩奇喜欢helpful。", "word": "helpful", "word_meaning": "喜欢helpful", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'helpful'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "hi": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes hi.", "sentence_zh": "佩奇喜欢hi。", "word": "hi", "word_meaning": "喜欢hi", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'hi'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "holiday": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes holiday.", "sentence_zh": "佩奇喜欢holiday。", "word": "holiday", "word_meaning": "喜欢holiday", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'holiday'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "hometown": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes hometown.", "sentence_zh": "佩奇喜欢hometown。", "word": "hometown", "word_meaning": "喜欢hometown", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'hometown'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "horse": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes horse.", "sentence_zh": "佩奇喜欢horse。", "word": "horse", "word_meaning": "喜欢horse", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'horse'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "hour": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes hour.", "sentence_zh": "佩奇喜欢hour。", "word": "hour", "word_meaning": "喜欢hour", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'hour'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "ice cream": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes ice cream.", "sentence_zh": "佩奇喜欢ice cream。", "word": "ice cream", "word_meaning": "喜欢ice cream", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'ice cream'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "ill": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes ill.", "sentence_zh": "佩奇喜欢ill。", "word": "ill", "word_meaning": "喜欢ill", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'ill'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "internet": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes internet.", "sentence_zh": "佩奇喜欢internet。", "word": "internet", "word_meaning": "喜欢internet", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'internet'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "its": {"grade": "primary", "sentences": [{"sentence": "Peppa likes its.", "sentence_zh": "佩奇喜欢its。", "word": "its", "word_meaning": "喜欢its", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'its'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "job": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes job.", "sentence_zh": "佩奇喜欢job。", "word": "job", "word_meaning": "喜欢job", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'job'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "kid": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes kid.", "sentence_zh": "佩奇喜欢kid。", "word": "kid", "word_meaning": "喜欢kid", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'kid'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "kite": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes kite.", "sentence_zh": "佩奇喜欢kite。", "word": "kite", "word_meaning": "喜欢kite", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'kite'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "lake": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes lake.", "sentence_zh": "佩奇喜欢lake。", "word": "lake", "word_meaning": "喜欢lake", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'lake'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "leg": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes leg.", "sentence_zh": "佩奇喜欢leg。", "word": "leg", "word_meaning": "喜欢leg", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'leg'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "letter": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes letter.", "sentence_zh": "佩奇喜欢letter。", "word": "letter", "word_meaning": "喜欢letter", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'letter'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "library": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes library.", "sentence_zh": "佩奇喜欢library。", "word": "library", "word_meaning": "喜欢library", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'library'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "lion": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes lion.", "sentence_zh": "佩奇喜欢lion。", "word": "lion", "word_meaning": "喜欢lion", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'lion'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "live": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes live.", "sentence_zh": "佩奇喜欢live。", "word": "live", "word_meaning": "喜欢live", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'live'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "man": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes man.", "sentence_zh": "佩奇喜欢man。", "word": "man", "word_meaning": "喜欢man", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'man'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "math": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes math.", "sentence_zh": "佩奇喜欢math。", "word": "math", "word_meaning": "喜欢math", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'math'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "mathematics": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes mathematics.", "sentence_zh": "佩奇喜欢mathematics。", "word": "mathematics", "word_meaning": "喜欢mathematics", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'mathematics'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "maths": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes maths.", "sentence_zh": "佩奇喜欢maths。", "word": "maths", "word_meaning": "喜欢maths", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'maths'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "meat": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes meat.", "sentence_zh": "佩奇喜欢meat。", "word": "meat", "word_meaning": "喜欢meat", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'meat'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "men": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes men.", "sentence_zh": "佩奇喜欢men。", "word": "men", "word_meaning": "喜欢men", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'men'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "mice": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes mice.", "sentence_zh": "佩奇喜欢mice。", "word": "mice", "word_meaning": "喜欢mice", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'mice'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "middle": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes middle.", "sentence_zh": "佩奇喜欢middle。", "word": "middle", "word_meaning": "喜欢middle", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'middle'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "milk": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes milk.", "sentence_zh": "佩奇喜欢milk。", "word": "milk", "word_meaning": "喜欢milk", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'milk'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "minute": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes minute.", "sentence_zh": "佩奇喜欢minute。", "word": "minute", "word_meaning": "喜欢minute", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'minute'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "mom": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes mom.", "sentence_zh": "佩奇喜欢mom。", "word": "mom", "word_meaning": "喜欢mom", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'mom'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "money": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes money.", "sentence_zh": "佩奇喜欢money。", "word": "money", "word_meaning": "喜欢money", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'money'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "monkey": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes monkey.", "sentence_zh": "佩奇喜欢monkey。", "word": "monkey", "word_meaning": "喜欢monkey", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'monkey'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "month": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes month.", "sentence_zh": "佩奇喜欢month。", "word": "month", "word_meaning": "喜欢month", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'month'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "mother": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes mother.", "sentence_zh": "佩奇喜欢mother。", "word": "mother", "word_meaning": "喜欢mother", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'mother'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "mouse": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes mouse.", "sentence_zh": "佩奇喜欢mouse。", "word": "mouse", "word_meaning": "喜欢mouse", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'mouse'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "mr.": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes mr..", "sentence_zh": "佩奇喜欢mr.。", "word": "mr.", "word_meaning": "喜欢mr.", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'mr.'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "mrs": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes mrs.", "sentence_zh": "佩奇喜欢mrs。", "word": "mrs", "word_meaning": "喜欢mrs", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'mrs'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "mrs.": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes mrs..", "sentence_zh": "佩奇喜欢mrs.。", "word": "mrs.", "word_meaning": "喜欢mrs.", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'mrs.'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "ms": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes ms.", "sentence_zh": "佩奇喜欢ms。", "word": "ms", "word_meaning": "喜欢ms", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'ms'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "ms.": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes ms..", "sentence_zh": "佩奇喜欢ms.。", "word": "ms.", "word_meaning": "喜欢ms.", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'ms.'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "near": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes near.", "sentence_zh": "佩奇喜欢near。", "word": "near", "word_meaning": "喜欢near", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'near'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "noodle": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes noodle.", "sentence_zh": "佩奇喜欢noodle。", "word": "noodle", "word_meaning": "喜欢noodle", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'noodle'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "o'clock": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes o'clock.", "sentence_zh": "佩奇喜欢o'clock。", "word": "o'clock", "word_meaning": "喜欢o'clock", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'o'clock'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "often": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes often.", "sentence_zh": "佩奇喜欢often。", "word": "often", "word_meaning": "喜欢often", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'often'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "ok": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes ok.", "sentence_zh": "佩奇喜欢ok。", "word": "ok", "word_meaning": "喜欢ok", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'ok'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "pair": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes pair.", "sentence_zh": "佩奇喜欢pair。", "word": "pair", "word_meaning": "喜欢pair", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'pair'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "panda": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes panda.", "sentence_zh": "佩奇喜欢panda。", "word": "panda", "word_meaning": "喜欢panda", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'panda'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "paper": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes paper.", "sentence_zh": "佩奇喜欢paper。", "word": "paper", "word_meaning": "喜欢paper", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'paper'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "parent": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes parent.", "sentence_zh": "佩奇喜欢parent。", "word": "parent", "word_meaning": "喜欢parent", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'parent'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "park": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes park.", "sentence_zh": "佩奇喜欢park。", "word": "park", "word_meaning": "喜欢park", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'park'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "pen": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes pen.", "sentence_zh": "佩奇喜欢pen。", "word": "pen", "word_meaning": "喜欢pen", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'pen'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "pencil": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes pencil.", "sentence_zh": "佩奇喜欢pencil。", "word": "pencil", "word_meaning": "喜欢pencil", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'pencil'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "pet": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes pet.", "sentence_zh": "佩奇喜欢pet。", "word": "pet", "word_meaning": "喜欢pet", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'pet'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "phone": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes phone.", "sentence_zh": "佩奇喜欢phone。", "word": "phone", "word_meaning": "喜欢phone", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'phone'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "photo": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes photo.", "sentence_zh": "佩奇喜欢photo。", "word": "photo", "word_meaning": "喜欢photo", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'photo'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "photograph": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes photograph.", "sentence_zh": "佩奇喜欢photograph。", "word": "photograph", "word_meaning": "喜欢photograph", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'photograph'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "physical education": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes physical education.", "sentence_zh": "佩奇喜欢physical education。", "word": "physical education", "word_meaning": "喜欢physical education", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'physical education'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "piano": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes piano.", "sentence_zh": "佩奇喜欢piano。", "word": "piano", "word_meaning": "喜欢piano", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'piano'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "ping-pong": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes ping-pong.", "sentence_zh": "佩奇喜欢ping-pong。", "word": "ping-pong", "word_meaning": "喜欢ping-pong", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'ping-pong'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "plane": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes plane.", "sentence_zh": "佩奇喜欢plane。", "word": "plane", "word_meaning": "喜欢plane", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'plane'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "plant": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes plant.", "sentence_zh": "佩奇喜欢plant。", "word": "plant", "word_meaning": "喜欢plant", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'plant'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "police": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes police.", "sentence_zh": "佩奇喜欢police。", "word": "police", "word_meaning": "喜欢police", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'police'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "potato": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes potato.", "sentence_zh": "佩奇喜欢potato。", "word": "potato", "word_meaning": "喜欢potato", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'potato'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "question": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes question.", "sentence_zh": "佩奇喜欢question。", "word": "question", "word_meaning": "喜欢question", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'question'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "quiet": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes quiet.", "sentence_zh": "佩奇喜欢quiet。", "word": "quiet", "word_meaning": "喜欢quiet", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'quiet'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "rice": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes rice.", "sentence_zh": "佩奇喜欢rice。", "word": "rice", "word_meaning": "喜欢rice", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'rice'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "robot": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes robot.", "sentence_zh": "佩奇喜欢robot。", "word": "robot", "word_meaning": "喜欢robot", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'robot'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "ruler": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes ruler.", "sentence_zh": "佩奇喜欢ruler。", "word": "ruler", "word_meaning": "喜欢ruler", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'ruler'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "schoolbag": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes schoolbag.", "sentence_zh": "佩奇喜欢schoolbag。", "word": "schoolbag", "word_meaning": "喜欢schoolbag", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'schoolbag'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "science": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes science.", "sentence_zh": "佩奇喜欢science。", "word": "science", "word_meaning": "喜欢science", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'science'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "season": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes season.", "sentence_zh": "佩奇喜欢season。", "word": "season", "word_meaning": "喜欢season", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'season'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "sell": {"grade": "primary", "sentences": [{"sentence": "Peppa likes sell.", "sentence_zh": "佩奇喜欢sell。", "word": "sell", "word_meaning": "喜欢sell", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'sell'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "ship": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes ship.", "sentence_zh": "佩奇喜欢ship。", "word": "ship", "word_meaning": "喜欢ship", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'ship'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "shoe": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes shoe.", "sentence_zh": "佩奇喜欢shoe。", "word": "shoe", "word_meaning": "喜欢shoe", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'shoe'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "shop": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes shop.", "sentence_zh": "佩奇喜欢shop。", "word": "shop", "word_meaning": "喜欢shop", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'shop'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "short": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes short.", "sentence_zh": "佩奇喜欢short。", "word": "short", "word_meaning": "喜欢short", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'short'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "shorts": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes shorts.", "sentence_zh": "佩奇喜欢shorts。", "word": "shorts", "word_meaning": "喜欢shorts", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'shorts'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "sing": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes sing.", "sentence_zh": "佩奇喜欢sing。", "word": "sing", "word_meaning": "喜欢sing", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'sing'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "sister": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes sister.", "sentence_zh": "佩奇喜欢sister。", "word": "sister", "word_meaning": "喜欢sister", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'sister'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "skirt": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes skirt.", "sentence_zh": "佩奇喜欢skirt。", "word": "skirt", "word_meaning": "喜欢skirt", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'skirt'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "sock": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes sock.", "sentence_zh": "佩奇喜欢sock。", "word": "sock", "word_meaning": "喜欢sock", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'sock'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "sometimes": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes sometimes.", "sentence_zh": "佩奇喜欢sometimes。", "word": "sometimes", "word_meaning": "喜欢sometimes", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'sometimes'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "song": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes song.", "sentence_zh": "佩奇喜欢song。", "word": "song", "word_meaning": "喜欢song", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'song'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "space": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes space.", "sentence_zh": "佩奇喜欢space。", "word": "space", "word_meaning": "喜欢space", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'space'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "sport": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes sport.", "sentence_zh": "佩奇喜欢sport。", "word": "sport", "word_meaning": "喜欢sport", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'sport'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "spring": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes spring.", "sentence_zh": "佩奇喜欢spring。", "word": "spring", "word_meaning": "喜欢spring", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'spring'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "stand": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes stand.", "sentence_zh": "佩奇喜欢stand。", "word": "stand", "word_meaning": "喜欢stand", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'stand'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "star": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes star.", "sentence_zh": "佩奇喜欢star。", "word": "star", "word_meaning": "喜欢star", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'star'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "street": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes street.", "sentence_zh": "佩奇喜欢street。", "word": "street", "word_meaning": "喜欢street", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'street'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "student": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes student.", "sentence_zh": "佩奇喜欢student。", "word": "student", "word_meaning": "喜欢student", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'student'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "subject": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes subject.", "sentence_zh": "佩奇喜欢subject。", "word": "subject", "word_meaning": "喜欢subject", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'subject'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "summer": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes summer.", "sentence_zh": "佩奇喜欢summer。", "word": "summer", "word_meaning": "喜欢summer", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'summer'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "sweater": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes sweater.", "sentence_zh": "佩奇喜欢sweater。", "word": "sweater", "word_meaning": "喜欢sweater", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'sweater'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "sweep": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes sweep.", "sentence_zh": "佩奇喜欢sweep。", "word": "sweep", "word_meaning": "喜欢sweep", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'sweep'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "swim": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes swim.", "sentence_zh": "佩奇喜欢swim。", "word": "swim", "word_meaning": "喜欢swim", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'swim'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "tail": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes tail.", "sentence_zh": "佩奇喜欢tail。", "word": "tail", "word_meaning": "喜欢tail", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'tail'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "taxi": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes taxi.", "sentence_zh": "佩奇喜欢taxi。", "word": "taxi", "word_meaning": "喜欢taxi", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'taxi'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "teacher": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes teacher.", "sentence_zh": "佩奇喜欢teacher。", "word": "teacher", "word_meaning": "喜欢teacher", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'teacher'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "thin": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes thin.", "sentence_zh": "佩奇喜欢thin。", "word": "thin", "word_meaning": "喜欢thin", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'thin'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "tiger": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes tiger.", "sentence_zh": "佩奇喜欢tiger。", "word": "tiger", "word_meaning": "喜欢tiger", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'tiger'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "toilet": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes toilet.", "sentence_zh": "佩奇喜欢toilet。", "word": "toilet", "word_meaning": "喜欢toilet", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'toilet'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "train": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes train.", "sentence_zh": "佩奇喜欢train。", "word": "train", "word_meaning": "喜欢train", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'train'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "travel": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes travel.", "sentence_zh": "佩奇喜欢travel。", "word": "travel", "word_meaning": "喜欢travel", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'travel'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "trousers": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes trousers.", "sentence_zh": "佩奇喜欢trousers。", "word": "trousers", "word_meaning": "喜欢trousers", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'trousers'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "umbrella": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes umbrella.", "sentence_zh": "佩奇喜欢umbrella。", "word": "umbrella", "word_meaning": "喜欢umbrella", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'umbrella'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "walk": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes walk.", "sentence_zh": "佩奇喜欢walk。", "word": "walk", "word_meaning": "喜欢walk", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'walk'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "weather": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes weather.", "sentence_zh": "佩奇喜欢weather。", "word": "weather", "word_meaning": "喜欢weather", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'weather'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "whose": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes whose.", "sentence_zh": "佩奇喜欢whose。", "word": "whose", "word_meaning": "喜欢whose", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'whose'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "window": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes window.", "sentence_zh": "佩奇喜欢window。", "word": "window", "word_meaning": "喜欢window", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'window'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "winter": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes winter.", "sentence_zh": "佩奇喜欢winter。", "word": "winter", "word_meaning": "喜欢winter", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'winter'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "wish": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes wish.", "sentence_zh": "佩奇喜欢wish。", "word": "wish", "word_meaning": "喜欢wish", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'wish'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "woman": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes woman.", "sentence_zh": "佩奇喜欢woman。", "word": "woman", "word_meaning": "喜欢woman", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'woman'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "women": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes women.", "sentence_zh": "佩奇喜欢women。", "word": "women", "word_meaning": "喜欢women", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'women'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "wonderful": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes wonderful.", "sentence_zh": "佩奇喜欢wonderful。", "word": "wonderful", "word_meaning": "喜欢wonderful", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'wonderful'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "word": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes word.", "sentence_zh": "佩奇喜欢word。", "word": "word", "word_meaning": "喜欢word", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'word'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "worker": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes worker.", "sentence_zh": "佩奇喜欢worker。", "word": "worker", "word_meaning": "喜欢worker", "difficulty_level": "level3", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'worker'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "world": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes world.", "sentence_zh": "佩奇喜欢world。", "word": "world", "word_meaning": "喜欢world", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'world'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "write": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes write.", "sentence_zh": "佩奇喜欢write。", "word": "write", "word_meaning": "喜欢write", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'write'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "year": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes year.", "sentence_zh": "佩奇喜欢year。", "word": "year", "word_meaning": "喜欢year", "difficulty_level": "level2", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'year'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}, "zoo": {"grade": "primary", "sentences": [{"sentence": "<PERSON><PERSON><PERSON> likes zoo.", "sentence_zh": "佩奇喜欢zoo。", "word": "zoo", "word_meaning": "喜欢zoo", "difficulty_level": "level1", "word_count": 3, "theme_integration": "使用佩奇角色展示单词'zoo'", "ip_id": "小猪佩奇_AI_GENERATED", "generation_method": "AI_API", "timestamp": "2025-08-05 23:09:04"}]}}}