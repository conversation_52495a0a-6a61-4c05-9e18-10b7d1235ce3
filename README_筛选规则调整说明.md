# 例句筛选规则调整说明

## 🎯 调整目标

针对小学例句难度较高的问题，对筛选规则进行了以下改进：

1. **分级筛选**：根据单词所属年级（小学/初中）采用不同的筛选标准
2. **翻译质量检查**：严格过滤中文翻译中包含英文单词的例句
3. **难度控制**：小学例句更严格控制词汇和语法复杂度

## 📊 主要改进

### 1. **分级词汇库系统**
- 自动加载小学词汇表（531个单词）
- 自动加载初中词汇表（1362个单词）
- 根据目标单词所属年级选择对应的筛选标准

### 2. **翻译质量评估（新增）**
- **满分条件**：纯中文翻译，无英文单词
- **严重扣分**：包含英文人名、短语（如"Moby Pig"、"muddy puddles"）
- **轻微扣分**：包含常见缩写（如"TV"、"OK"）
- **完全不合格**：空翻译或翻译失败

### 3. **分级筛选标准差异**

#### 小学筛选标准（更严格）
- **句子长度**：4-10个词为最佳（原来是4-15个词）
- **词汇控制**：最多允许1个非小学词汇（原来允许2-4个）
- **语法复杂度**：严格避免复杂语法结构
- **内容偏向**：更强调儿童友好、具体形象的内容

#### 初中筛选标准（适度放宽）
- **句子长度**：6-15个词为最佳
- **词汇控制**：允许适量初中词汇（最多3-5个非基础词汇）
- **语法复杂度**：可以接受适度的复杂语法结构
- **内容范围**：可以包含更多主题和抽象概念

### 4. **权重调整**

#### 小学权重分配
```
完整性: 15%    长度控制: 20% ⬆️    词义清晰: 20%
难度控制: 25% ⬆️    正向性: 10%    多样性: 5%
对话性: 3%    丰富度: 2%    翻译质量: 0%
```

#### 初中权重分配
```
完整性: 15%    长度控制: 12%    词义清晰: 18%
难度控制: 15%    正向性: 8%    多样性: 10%
对话性: 5%    丰富度: 7%    翻译质量: 10% ⬆️
```

## 🔧 使用方法

### 自动分级筛选
系统会自动根据单词所属年级选择筛选标准：

```python
# 系统自动判断
if word.lower() in primary_vocabulary:
    evaluator = SentenceQualityEvaluator(grade_level="primary")
    print(f"单词'{word}'属于小学词汇，使用小学筛选标准")
else:
    evaluator = SentenceQualityEvaluator(grade_level="junior")
    print(f"单词'{word}'属于初中词汇，使用初中筛选标准")
```

### 翻译质量检查
在筛选过程中自动过滤翻译质量不佳的例句：

```python
# 检查翻译质量
translation_score = evaluator.evaluate_translation_quality(match['sentence_zh'])
if translation_score >= 5:  # 翻译质量阈值
    valid_matches.append(match)
else:
    print(f"跳过翻译质量不佳的例句: {match['sentence_zh']}")
```

## 📈 效果对比

### 翻译质量检查效果
- ✅ "这是一个很好的例子。" → 10/10分
- ❌ "这是Moby Pig，和这是Daddy Pig。" → 1/10分
- ❌ "我知道你一直在 muddy puddles 里跳跃。" → 4/10分

### 分级筛选效果
**简单句子**（`I like apples.`）：
- 小学评估：13.05分 ✅ 更适合小学
- 初中评估：11.83分

**复杂句子**（`Although it was raining...`）：
- 小学评估：5.37分（严格扣分）
- 初中评估：8.95分 ✅ 相对更能接受

## 🎯 预期效果

1. **小学例句**：更短、更简单、更具体、更儿童友好
2. **初中例句**：适度复杂、词汇丰富、语法稍复杂但仍可理解
3. **翻译质量**：严格过滤包含英文的中文翻译，确保例句质量
4. **学习适配**：根据不同年级的学习特点提供合适的例句难度

## 📝 注意事项

1. 词汇表文件路径：`data/小学单词表.txt` 和 `data/初中单词表.txt`
2. 翻译质量阈值可根据实际需要调整（当前设为5分）
3. 允许的专有名词列表可根据具体内容扩展
4. 分级标准可根据实际使用效果进一步优化
