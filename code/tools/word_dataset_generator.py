import os
import re
import json
import pandas as pd
import time
from pathlib import Path
from collections import defaultdict, Counter
from typing import List, Dict, Tuple, Set

# 添加NLTK相关导入
import nltk
from nltk.tokenize import word_tokenize
from nltk.tag import pos_tag
from nltk.corpus import stopwords

# 导入翻译工具
from .translate_utils import translate_with_llm
from .concurrent_translate import translate_sentence_batch

# 导入句子质量评估器
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from service.reference.sentence_selector import SentenceQualityEvaluator

# 在导入NLTK相关模块后，修改下载部分

# 下载必要的NLTK数据
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('taggers/averaged_perceptron_tagger')
except LookupError:
    nltk.download('averaged_perceptron_tagger')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

try:
    nltk.data.find('tokenizers/punkt_tab')
except LookupError:
    nltk.download('punkt_tab')

try:
    nltk.data.find('taggers/averaged_perceptron_tagger_eng')
except LookupError:
    nltk.download('averaged_perceptron_tagger_eng')


def _load_vocabulary_file(file_path):
    """加载词汇表文件"""
    try:
        import os
        # 获取项目根目录 - 从当前文件向上三级到项目根目录
        current_file = os.path.abspath(__file__)
        # code/tools/word_dataset_generator.py -> code/tools -> code -> project_root
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
        full_path = os.path.join(project_root, file_path)

        vocabulary = set()
        with open(full_path, 'r', encoding='utf-8') as f:
            for line in f:
                word = line.strip().lower()
                if word:
                    vocabulary.add(word)
        return vocabulary
    except FileNotFoundError:
        print(f"警告: 词汇表文件 {file_path} 未找到，返回空集合")
        return set()

def _is_too_similar(new_sentence: str, existing_sentences: List[Dict]) -> bool:
    """检查新句子是否与已选句子过于相似"""
    if not existing_sentences:
        return False

    new_words = set(word_tokenize(new_sentence.lower()))

    for existing in existing_sentences:
        existing_words = set(word_tokenize(existing['sentence'].lower()))

        # 计算词汇重叠度
        overlap = len(new_words & existing_words) / len(new_words | existing_words)

        # 如果重叠度超过70%，认为过于相似
        if overlap > 0.7:
            return True

    return False

def generate_word_dataset(word_list_path, ip_data_dir, max_sentences_per_word=7, target_grade=None):
    """根据单词列表从字幕中匹配单词并生成数据集

    参数:
        word_list_path: 单词列表文件路径
        ip_data_dir: IP动画数据目录
        max_sentences_per_word: 每个单词最多选择的例句数量
        target_grade: 目标年级 ("primary", "junior", None表示自动处理所有年级)
    返回:
        tuple: (数据集文件路径列表, 报告文件路径列表)
    """
    # 读取单词列表
    with open(word_list_path, 'r', encoding='utf-8') as f:
        word_list = [line.strip() for line in f if line.strip()]
    output_dir = str(Path(ip_data_dir).parent)

    # 加载小学和初中词汇表用于分级（总是需要加载，因为后续处理需要用到）
    primary_vocabulary = _load_vocabulary_file("data/小学单词表.txt")
    junior_vocabulary = _load_vocabulary_file("data/初中单词表.txt")

    # 检查是否是标准词汇表文件
    is_standard_vocab_file = False
    if target_grade:
        # 检查文件路径是否包含标准词汇表的路径
        if ("小学单词表.txt" in word_list_path and target_grade == "primary") or \
           ("初中单词表.txt" in word_list_path and target_grade == "junior"):
            is_standard_vocab_file = True
            print(f"检测到标准{target_grade}词汇表文件，将处理全部{len(word_list)}个单词")

    # 确定处理策略
    datasets_to_generate = []

    if is_standard_vocab_file:
        # 如果是标准词汇表文件，直接处理全部单词
        datasets_to_generate.append((target_grade, word_list))
    else:

        # 分析单词列表的年级分布
        primary_words = [word for word in word_list if word.lower() in primary_vocabulary]
        junior_words = [word for word in word_list if word.lower() in junior_vocabulary and word.lower() not in primary_vocabulary]
        mixed_words = [word for word in word_list if word.lower() not in primary_vocabulary and word.lower() not in junior_vocabulary]

        print(f"单词分析结果:")
        print(f"  小学词汇: {len(primary_words)} 个")
        print(f"  初中词汇: {len(junior_words)} 个")
        print(f"  其他词汇: {len(mixed_words)} 个")

        # 如果指定了目标年级，只处理该年级的词汇
        if target_grade == "primary":
            if primary_words:
                datasets_to_generate.append(("primary", primary_words))
            else:
                # 如果没有找到小学词汇，但指定了小学年级，说明输入的就是小学词汇表
                print("输入的单词列表将作为小学词汇处理")
                datasets_to_generate.append(("primary", word_list))
        elif target_grade == "junior":
            if junior_words:
                datasets_to_generate.append(("junior", junior_words))
            else:
                # 如果没有找到初中词汇，但指定了初中年级，说明输入的就是初中词汇表
                print("输入的单词列表将作为初中词汇处理")
                datasets_to_generate.append(("junior", word_list))
        else:
            # 自动处理所有年级
            if primary_words:
                datasets_to_generate.append(("primary", primary_words))
            if junior_words:
                datasets_to_generate.append(("junior", junior_words))
            if mixed_words:
                datasets_to_generate.append(("mixed", mixed_words))

    if not datasets_to_generate:
        if target_grade:
            raise ValueError(f"没有找到{target_grade}年级的单词")
        else:
            raise ValueError("没有找到可处理的单词")

    # 如果只有一种类型的词汇，使用原IP目录；否则创建分级目录
    if len(datasets_to_generate) == 1:
        use_grade_suffix = False
    else:
        use_grade_suffix = True

    print(f"成功读取单词列表，共{len(word_list)}个单词")

    # 查找字幕图片关联CSV文件
    subtitles_images_path = os.path.join(ip_data_dir, "subtitles_images.csv")

    if not os.path.exists(subtitles_images_path):
        raise FileNotFoundError(f"未找到字幕图片关联文件：{subtitles_images_path}")
    else:
        print(f"使用字幕图片关联文件：{subtitles_images_path}")
        df = pd.read_csv(subtitles_images_path)
        # 从CSV文件中获取IP ID
        if 'ip_id' in df.columns:
            ip_id = df['ip_id'].iloc[0]
        else:
            raise KeyError("CSV文件中缺少ip_id列")
    print(f"成功读取字幕数据，共{len(df)}行")

    # 存储所有生成的文件路径
    output_files = []
    report_files = []

    # 为每个年级生成独立的数据集
    for grade_level, words_for_grade in datasets_to_generate:
        print(f"\n开始处理{grade_level}年级词汇，共{len(words_for_grade)}个单词")

        # 创建输出目录
        if use_grade_suffix:
            grade_output_dir = os.path.join(output_dir, f"{Path(ip_data_dir).name}_{grade_level}")
        else:
            grade_output_dir = ip_data_dir

        os.makedirs(grade_output_dir, exist_ok=True)

        # 处理当前年级的单词
        result_data, stats = _process_words_for_grade(
            words_for_grade, grade_level, df, ip_id, max_sentences_per_word,
            primary_vocabulary, junior_vocabulary
        )

        # 保存结果
        output_file, report_file = _save_grade_dataset(
            result_data, stats, grade_level, grade_output_dir, ip_id
        )

        output_files.append(output_file)
        report_files.append(report_file)

    return output_files, report_files


def _process_words_for_grade(words_list, grade_level, df, ip_id, max_sentences_per_word,
                           primary_vocabulary, junior_vocabulary):
    """处理特定年级的单词列表"""

    # 创建评估器
    evaluator = SentenceQualityEvaluator(grade_level=grade_level)

    # 统计信息
    stats = {
        'grade_level': grade_level,
        'total_words': len(words_list),
        'matched_words': 0,
        'unmatched_words': 0,
        'total_sentences': 0,
        'translation_failed': 0,
        'quality_filtered': 0,
        'word_details': {}
    }

    result_data = {}

    # 处理每个单词
    for word in words_list:
        print(f"处理单词：{word} ({grade_level}年级)")

        # 记录单词详细信息
        word_detail = {
            'word': word,
            'grade': grade_level,
            'raw_matches': 0,
            'final_sentences': 0,
            'translation_failed': 0,
            'quality_filtered': 0
        }

        all_matches = []

        # 遍历字幕行
        for _, row in df.iterrows():
            try:
                timestamp = row['timestamp']
                sentence = row['sentence']

                sentence = str(sentence)
                if not re.search(r'[a-zA-Z]', sentence):
                    continue

                # 检查单词是否在句子中
                if re.search(r'\b' + re.escape(word.lower()) + r'\b', sentence.lower()):
                    match_data = {
                        'timestamp': timestamp,
                        'sentence': sentence,
                        'image_id': row.get('image_id', ''),
                        'ip_id': ip_id
                    }
                    all_matches.append(match_data)

            except Exception as e:
                print(f"处理行时出错: {e}")
                continue

        # 更新匹配统计
        word_detail['raw_matches'] = len(all_matches)

        # 如果有匹配结果，进行筛选
        if all_matches:
            print(f"单词'{word}'匹配到{len(all_matches)}个结果，开始筛选...")

            # 为每个句子计算质量分数
            word_patterns = set()  # 记录该单词已使用的句式模式
            scored_matches = []

            for match in all_matches:
                sentence_text = match['sentence']
                # 暂时不传入翻译，因为还没有翻译
                scores = evaluator.calculate_total_score(sentence_text, word, word_patterns)

                match['scores'] = scores
                match['total_score'] = scores['total']
                scored_matches.append(match)

            # 按总分排序
            scored_matches.sort(key=lambda x: x['total_score'], reverse=True)


            # 选择最佳句子并准备批量翻译
            translation_candidates = []
            for match in scored_matches[:max_sentences_per_word * 2]:
                sentence_text = match['sentence']

                # 避免选择过于相似的句子
                if _is_too_similar(sentence_text, translation_candidates):
                    continue

                # 准备翻译数据
                translation_item = match.copy()
                del translation_item['scores']
                del translation_item['total_score']
                translation_item['word'] = word  # 添加单词信息用于翻译
                translation_candidates.append(translation_item)

            # 使用并发翻译处理所有候选句子
            try:
                translated_matches = translate_sentence_batch(translation_candidates, max_workers=10)

                # 检查翻译结果是否为空（翻译服务不可用的情况）
                if not translated_matches:
                    print("翻译服务不可用，使用原始英文句子继续处理...")
                    # 如果翻译失败，使用原始英文句子
                    translated_matches = []
                    for candidate in translation_candidates:
                        fallback_match = candidate.copy()
                        fallback_match['word_meaning'] = word  # 使用单词本身作为含义
                        fallback_match['sentence_zh'] = candidate['sentence']  # 使用英文原句
                        translated_matches.append(fallback_match)
                    word_detail['translation_failed'] = len(translation_candidates)
                    stats['translation_failed'] += len(translation_candidates)

            except Exception as e:
                print(f"并发翻译时出错: {e}")
                print("使用原始英文句子继续处理...")
                # 如果翻译失败，使用原始英文句子
                translated_matches = []
                for candidate in translation_candidates:
                    fallback_match = candidate.copy()
                    fallback_match['word_meaning'] = word  # 使用单词本身作为含义
                    fallback_match['sentence_zh'] = candidate['sentence']  # 使用英文原句
                    translated_matches.append(fallback_match)
                word_detail['translation_failed'] = len(translation_candidates)
                stats['translation_failed'] += len(translation_candidates)

            # 过滤掉翻译失败的结果，并进行翻译质量检查
            valid_matches = []
            translation_failed_count = 0
            quality_filtered_count = 0

            for match in translated_matches:
                if match.get('word_meaning') and match.get('sentence_zh'):
                    # 检查翻译质量（如果是英文原句，跳过质量检查）
                    if match['sentence_zh'] == match['sentence']:
                        # 使用英文原句，跳过翻译质量检查
                        translation_score = 10  # 给予高分
                        print(f"    使用英文原句: {match['sentence']}")
                    else:
                        # 正常的翻译质量检查
                        translation_score = evaluator.evaluate_translation_quality(match['sentence_zh'])

                    if translation_score >= 5:  # 翻译质量阈值
                        # 添加年级标注
                        match['grade'] = grade_level
                        valid_matches.append(match)
                    else:
                        print(f"    跳过翻译质量不佳的例句: {match['sentence_zh']}")
                        quality_filtered_count += 1
                else:
                    translation_failed_count += 1

            selected_matches = valid_matches[:max_sentences_per_word]
            result_data[word] = {
                'grade': grade_level,
                'sentences': selected_matches
            }

            # 更新统计信息
            word_detail['final_sentences'] = len(selected_matches)
            word_detail['translation_failed'] = translation_failed_count
            word_detail['quality_filtered'] = quality_filtered_count

            stats['matched_words'] += 1
            stats['total_sentences'] += len(selected_matches)
            stats['translation_failed'] += translation_failed_count
            stats['quality_filtered'] += quality_filtered_count

            print(f"单词'{word}'筛选后保留{len(selected_matches)}个高质量例句")
        else:
            print(f"单词'{word}'没有匹配到任何结果")
            stats['unmatched_words'] += 1

        stats['word_details'][word] = word_detail

    return result_data, stats


def _save_grade_dataset(result_data, stats, grade_level, output_dir, ip_id):
    """保存特定年级的数据集"""
    from datetime import datetime

    # 创建带元数据的JSON结构
    dataset_with_metadata = {
        'metadata': {
            'grade_level': grade_level,
            'ip_id': ip_id,
            'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_words': stats['total_words'],
            'matched_words': stats['matched_words'],
            'total_sentences': stats['total_sentences']
        },
        'words': result_data
    }

    # 保存JSON文件
    output_file = Path(output_dir) / f"word_dataset_{grade_level}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(dataset_with_metadata, f, ensure_ascii=False, indent=2)

    # 生成详细报告
    report_file = _generate_grade_dataset_report(stats, output_file, grade_level)

    print(f"成功生成{grade_level}年级数据集，共{len(result_data)}个单词有匹配结果")
    print(f"结果已保存到：{output_file}")
    print(f"详细报告已保存到：{report_file}")

    return output_file, report_file

def _generate_grade_dataset_report(stats, output_file, grade_level):
    """生成特定年级的数据集处理报告"""
    from datetime import datetime

    report_path = str(output_file).replace('.json', '_report.md')

    # 计算成功率
    match_rate = (stats['matched_words'] / stats['total_words'] * 100) if stats['total_words'] > 0 else 0
    avg_sentences = (stats['total_sentences'] / stats['matched_words']) if stats['matched_words'] > 0 else 0

    # 年级标识
    grade_icon = "🎓" if grade_level == 'primary' else "📚" if grade_level == 'junior' else "📖"
    grade_name = {"primary": "小学", "junior": "初中", "mixed": "混合"}.get(grade_level, grade_level)

    report_content = f"""# {grade_name}词汇数据集生成报告 {grade_icon}

## 📊 总体统计

- **处理时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **年级水平**: {grade_name}
- **输入单词总数**: {stats['total_words']}
- **成功匹配单词**: {stats['matched_words']} ({match_rate:.1f}%)
- **未匹配单词**: {stats['unmatched_words']}
- **生成例句总数**: {stats['total_sentences']}
- **平均每词例句数**: {avg_sentences:.1f}

## 🔍 质量控制

- **翻译失败**: {stats['translation_failed']} 条例句
- **质量过滤**: {stats['quality_filtered']} 条例句（包含英文等问题）

## 📝 单词详情

### 成功匹配的单词
"""

    # 添加成功匹配的单词详情
    matched_words = [detail for detail in stats['word_details'].values() if detail['final_sentences'] > 0]
    matched_words.sort(key=lambda x: x['final_sentences'], reverse=True)

    for detail in matched_words:
        report_content += f"- **{detail['word']}**: {detail['final_sentences']} 例句 (原始匹配: {detail['raw_matches']})\n"

    # 添加未匹配的单词
    unmatched_words = [detail for detail in stats['word_details'].values() if detail['final_sentences'] == 0]
    if unmatched_words:
        report_content += f"\n### 未匹配的单词\n"
        for detail in unmatched_words:
            report_content += f"- **{detail['word']}**: 无匹配结果\n"

    report_content += f"""
## 📁 输出文件

- **数据集文件**: `{output_file.name}`
- **报告文件**: `{report_path.split('/')[-1]}`

## 📋 数据结构

此数据集使用新的分级结构：
```json
{{
  "metadata": {{
    "grade_level": "{grade_level}",
    "ip_id": "...",
    "generation_time": "...",
    "total_words": ...,
    "matched_words": ...,
    "total_sentences": ...
  }},
  "words": {{
    "单词": {{
      "grade": "{grade_level}",
      "sentences": [...]
    }}
  }}
}}
```

---
*报告由系统自动生成*
"""

    # 保存报告
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)

    return report_path


def time_str_to_ms(time_str):
    """将时间字符串转换为毫秒"""
    # 处理可能带有引号的情况
    if isinstance(time_str, str) and time_str.startswith('"') and time_str.endswith('"'):
        time_str = time_str[1:-1]

    # 处理可能包含 --> 分隔符的情况
    if isinstance(time_str, str):
        if '-->' in time_str:
            time_str = time_str.split('-->')[0].strip()
        elif ' --> ' in time_str:
            time_str = time_str.split(' --> ')[0].strip()

    # 兼容两种分隔符：逗号和点号
    if isinstance(time_str, str):
        if ',' in time_str:
            hh_mm_ss, ms = time_str.split(',')
        elif '.' in time_str:
            hh_mm_ss, ms = time_str.split('.')
        else:
            # 如果没有毫秒部分，默认为0
            hh_mm_ss = time_str
            ms = '0'

        h, m, s = hh_mm_ss.split(':')
        total_ms = int(h) * 3600 * 1000 + int(m) * 60 * 1000 + int(s) * 1000 + int(ms)
        return total_ms
    else:
        raise TypeError(f"Expected string or bytes-like object, got {type(time_str)}")


def _load_vocabulary_file(file_path):
    """加载词汇表文件"""
    try:
        import os
        # 获取项目根目录 - 从当前文件向上三级到项目根目录
        current_file = os.path.abspath(__file__)
        # code/tools/word_dataset_generator.py -> code/tools -> code -> project_root
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
        full_path = os.path.join(project_root, file_path)

        vocabulary = set()
        with open(full_path, 'r', encoding='utf-8') as f:
            for line in f:
                word = line.strip().lower()
                if word:
                    vocabulary.add(word)
        return vocabulary
    except FileNotFoundError:
        print(f"警告: 词汇表文件 {file_path} 未找到，返回空集合")
        return set()

def _is_too_similar(new_sentence: str, existing_sentences: List[Dict]) -> bool:
    """检查新句子是否与已选句子过于相似"""
    if not existing_sentences:
        return False

    new_words = set(word_tokenize(new_sentence.lower()))

    for existing in existing_sentences:
        existing_words = set(word_tokenize(existing['sentence'].lower()))

        # 计算词汇重叠度
        overlap = len(new_words & existing_words) / len(new_words | existing_words)

        # 如果重叠度超过70%，认为过于相似
        if overlap > 0.7:
            return True

    return False


