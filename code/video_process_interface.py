# 在文件顶部添加系统路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import streamlit as st
import time
import shutil
from pathlib import Path
import tempfile
import pandas as pd
from service.video_processor import VideoProcessor
from service.ui_processor import UIProcessor  # 新增服务层处理类
from external.oss_utils import OssManager
from external.aliyun_asr import AliyunASR

# 自定义输出重定向类，用于捕获处理过程中的输出并显示在界面上
class StreamlitLogger:
    def __init__(self, log_placeholder):
        self.log_placeholder = log_placeholder
        self.log_text = ""

    def write(self, text):
        self.log_text += text
        self.log_placeholder.text_area("处理日志", self.log_text, height=300)

    def flush(self):
        pass

# 初始化OSS管理器
oss_manager = OssManager()

# 设置页面标题
st.set_page_config(page_title="视频处理工具", layout="wide")

# 标题和说明
st.title("视频处理工具")
st.markdown("""
这个工具可以帮助您：
1. 从视频中提取音频
2. 将音频转换为带时间戳的字幕
3. 根据字幕时间戳从视频中提取帧
4. 自动将文件保存到本地
""")

# 创建项目根目录下的output文件夹
project_root = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
output_dir = project_root / "output"
os.makedirs(output_dir, exist_ok=True)

# 创建临时目录用于存储上传的视频
temp_dir = tempfile.mkdtemp()

# 注册清理函数
def cleanup():
    try:
        shutil.rmtree(temp_dir)
    except:
        pass

import atexit
atexit.register(cleanup)


# 创建UI处理器实例
ui_processor = UIProcessor(project_root, output_dir)

# 创建界面占位符
progress_placeholder = st.empty()
status_placeholder = st.empty()
log_placeholder = st.empty()
result_placeholder = st.empty()

# 在侧边栏 - 参数设置和功能选择部分
with st.sidebar:
    st.header("功能选择")
    
    # 添加功能选择选项
    function_mode = st.radio(
        "选择功能模式",
        ["视频自动处理", "视频+字幕处理", "单词数据集生成"]
    )
    
    st.header("参数设置")

    # 音频时间调整设置
    col1, col2 = st.columns(2)
    with col1:
        start_buffer = st.slider(
            "音频开始提前时间（秒）",
            min_value=0.0,
            max_value=0.5,
            value=0.1,
            step=0.05,
            help="在字幕开始时间基础上提前的秒数"
        )
    with col2:
        audio_buffer = st.slider(
            "音频结束延长时间（秒）",
            min_value=0.0,
            max_value=2.0,
            value=0.8,
            step=0.1,
            help="在字幕结束时间基础上延长的秒数"
        )

    # 添加OSS设置选项 - 这部分保留并设为必选
    use_oss = st.checkbox("使用阿里云OSS存储", value=True, disabled=True)
    if not oss_manager.available:
        st.error("阿里云OSS访问凭证未设置，请配置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET")
        st.info("使用阿里云听悟API必须启用OSS存储")

# 主处理逻辑 - 根据选择的功能模式显示不同的界面
if function_mode == "视频自动处理":
    # 视频自动处理模式
    uploaded_file = st.file_uploader("上传视频文件", type=["mp4", "avi", "mov", "mkv"], accept_multiple_files=False, key="video_auto")
    
    # 获取IP数据集选项
    ip_options = ui_processor.get_ip_options(output_dir)
    # selected_ip = st.selectbox("选择已有IP数据集（选择none创建新的IP数据集）", ip_options)
    
    if uploaded_file:
        # 显示上传的文件
        st.write(f"已上传文件: {uploaded_file.name}")
        
        # 添加处理按钮
        if st.button("开始处理"):
            # 检查OSS是否可用
            if not oss_manager.available:
                st.error("阿里云OSS服务不可用，请检查凭证设置")
            else:
                # 保存上传的文件到临时目录
                video_path = os.path.join(temp_dir, uploaded_file.name)
                
                # 保存文件
                with open(video_path, "wb") as f:
                    f.write(uploaded_file.getbuffer())
                
                # 创建输出目录
                video_output_dir = os.path.join(output_dir, "videos", uploaded_file.name.split('.')[0])
                
                # 创建日志记录器
                logger = StreamlitLogger(log_placeholder)
                
                # 处理视频
                ui_processor.process_video(
                    video_path, 
                    video_output_dir, 
                    logger,
                    progress_placeholder,
                    status_placeholder,
                    result_placeholder
                )

elif function_mode == "视频+字幕处理":
    # 视频+字幕处理模式
    col1, col2 = st.columns(2)
    
    with col1:
        uploaded_video = st.file_uploader("上传视频文件", type=["mp4", "avi", "mov", "mkv"], accept_multiple_files=False, key="video_subtitle")
    
    with col2:
        uploaded_subtitle = st.file_uploader("上传字幕文件", type=["srt", "vtt", "ass", "lrc"], key="subtitle")
    
    # 获取IP元数据和显示选项
    ip_display_names, ip_options = ui_processor.get_ip_metadata_options(project_root, include_empty=True)
    ip_display_names.insert(0, "创建新的IP数据集")
    default_index = 0
    selected_display = st.selectbox("选择IP动画数据",
                                    ip_display_names if ip_display_names else ["无可用IP数据"],
                                    index=default_index)
    
    # 获取选择的IP ID
    selected_ip = ui_processor.get_selected_ip_id(ip_options, selected_display)
    
    if uploaded_video and uploaded_subtitle:
        # 显示上传的文件
        st.write(f"已上传视频文件: {uploaded_video.name}")
        st.write(f"已上传字幕文件: {uploaded_subtitle.name}")
        
        # 添加处理按钮
        if st.button("开始处理"):
            # 保存上传的文件到临时目录
            video_path = os.path.join(temp_dir, uploaded_video.name)
            subtitle_path = os.path.join(temp_dir, uploaded_subtitle.name)
            
            # 保存文件
            with open(video_path, "wb") as f:
                f.write(uploaded_video.getbuffer())
                
            with open(subtitle_path, "wb") as f:
                f.write(uploaded_subtitle.getbuffer())
            
            # 创建日志记录器
            logger = StreamlitLogger(log_placeholder)
            
            # 处理视频和字幕
            ui_processor.process_video_with_subtitle(
                video_path,
                subtitle_path,
                selected_ip,
                logger,
                progress_placeholder,
                status_placeholder,
                result_placeholder,
                audio_buffer,
                start_buffer
            )

elif function_mode == "单词数据集生成":
    # 单词数据集生成模式
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📝 选择单词列表")

        # 添加选择方式的单选按钮
        word_list_option = st.radio(
            "选择单词列表来源:",
            ["上传文件", "使用本地词库"],
            key="word_list_source"
        )

        uploaded_word_list = None
        local_word_list_path = None

        if word_list_option == "上传文件":
            uploaded_word_list = st.file_uploader("上传单词列表文件", type=["txt"], key="word_list")
        else:
            # 使用本地词库
            local_vocab_options = {
                "小学词库 (531个单词)": "data/小学单词表.txt",
                "初中词库 (1362个单词)": "data/初中单词表.txt"
            }

            selected_vocab = st.selectbox(
                "选择本地词库:",
                list(local_vocab_options.keys()),
                key="local_vocab"
            )

            local_word_list_path = local_vocab_options[selected_vocab]

            # 显示词库信息
            if local_word_list_path:
                full_path = os.path.join(project_root, local_word_list_path)
                if os.path.exists(full_path):
                    with open(full_path, 'r', encoding='utf-8') as f:
                        word_count = len([line.strip() for line in f if line.strip()])
                    st.info(f"📚 已选择: {selected_vocab}\n📍 路径: {local_word_list_path}\n📊 单词数量: {word_count}")
                else:
                    st.error(f"❌ 词库文件不存在: {local_word_list_path}")

    with col2:
        st.subheader("🎬 选择IP动画数据")
        # 获取IP元数据和显示选项
        ip_display_names, ip_options = ui_processor.get_ip_metadata_options(project_root, include_empty=True)
        default_index = 0
        selected_display = st.selectbox("选择IP动画数据",
                                        ip_display_names if ip_display_names else ["无可用IP数据"],
                                        index=default_index)
        # 获取选择的IP ID
        selected_ip = ui_processor.get_selected_ip_id(ip_options, selected_display)


    # 检查是否有可用的单词列表和IP数据
    has_word_list = (uploaded_word_list is not None) or (local_word_list_path is not None)

    if has_word_list and selected_ip != "none":
        # 显示选择的文件和IP信息
        st.markdown("---")
        st.subheader("🚀 准备生成数据集")

        if uploaded_word_list:
            st.write(f"📝 单词列表文件: {uploaded_word_list.name}")
        else:
            st.write(f"📚 本地词库: {selected_vocab}")

        st.write(f"🎬 IP动画数据: {selected_display}")

        # 添加处理按钮
        if st.button("开始生成数据集", type="primary"):
            # 确定单词列表文件路径
            if uploaded_word_list:
                # 保存上传的文件到临时目录
                word_list_path = os.path.join(temp_dir, uploaded_word_list.name)
                with open(word_list_path, "wb") as f:
                    f.write(uploaded_word_list.getbuffer())
                target_grade = None  # 上传文件时自动判断年级
            else:
                # 使用本地词库文件
                word_list_path = os.path.join(project_root, local_word_list_path)
                # 根据选择的词库确定目标年级
                if "小学" in selected_vocab:
                    target_grade = "primary"
                elif "初中" in selected_vocab:
                    target_grade = "junior"
                else:
                    target_grade = None

            # 创建日志记录器
            logger = StreamlitLogger(log_placeholder)

            # 生成单词数据集
            ui_processor.generate_word_dataset(
                word_list_path,
                selected_ip,
                logger,
                status_placeholder,
                result_placeholder,
                target_grade=target_grade
            )

    elif selected_ip == "none":
        st.warning("⚠️ 请先使用模式二创建IP动画数据")
    elif not has_word_list:
        if word_list_option == "上传文件":
            st.info("📤 请上传单词列表文件")
        else:
            st.info("📚 请选择本地词库文件")