import os
import json
import logging
from openai import OpenAI
from pathlib import Path

class LlmService:
    def __init__(self, model_name="qwen3-30b-a3b-instruct-2507", api_key=None):
        """
        :param model_name: 使用的模型名称
        :param api_key: API密钥，如果为None则从环境变量获取
        """
        self.model_name = model_name
        # 从环境变量或参数获取API密钥
        self.api_key = api_key or os.environ.get('WUJIE_ALIYUN_API_KEY')
        
        # 验证必要参数
        if not self.api_key:
            logging.warning("API Key未设置，翻译功能将不可用。请设置WUJIE_ALIYUN_API_KEY环境变量。")
            self.available = False
            return
        
        try:    
            # 初始化OpenAI客户端
            self.client = OpenAI(
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1", 
                api_key=self.api_key
            )
            self.available = True
        except Exception as e:
            logging.error(f"初始化LLM客户端失败: {str(e)}")
            self.available = False
    
    def llm_chat_json(self, messages):
        """
        使用LLM进行JSON格式的聊天补全
        
        :param messages: 消息列表，包含角色和内容
        :return: 解析后的JSON响应，失败时返回None
        """
        if not self.available:
            logging.warning("LLM服务不可用，无法进行聊天补全")
            return None
            
        try:
            # 调用OpenAI API
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                response_format={"type": "json_object"}
            )
            
            # 解析响应
            result_text = response.choices[0].message.content.strip()
            
            # 解析JSON
            try:
                return json.loads(result_text)
            except json.JSONDecodeError:
                logging.error(f"无法解析LLM响应为JSON: {result_text}")
                return None
                
        except Exception as e:
            logging.error(f"LLM聊天补全时发生错误: {str(e)}")
            return None